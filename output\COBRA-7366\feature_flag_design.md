# Feature Flag Design for Separate Combined Bundles Jobs

## Overview
This document outlines the design for implementing a feature flag system to control whether combined bundles are created in separate Jenkins jobs or inline within frosty/patchfrosty jobs.

## Current State Analysis

### Existing Combined Bundles Configuration
Currently, branches use a `combine_bundles` map in their settings:

```groovy
combine_bundles: [
    combine_asset        : 'CombinedShippingMPLevels',
    combine_reference_job: 'CH1-SP-content-dev-first-patch.patchdata.start',
    is_target_branch     : true,
    source_branch_code   : 'CH1-SP-content-dev',
    source_branch_data   : 'CH1-SP-content-dev-first-patch',
]
```

### Existing Implementation
- `combined_bundles.py` - Standalone script for creating combined bundles
- `frosty.py` - Has `--use-precreated-combined-bundles` flag
- `patch_frosty.py` - Creates combined bundles inline (no pre-created option yet)

## Feature Flag Design

### 1. Branch Settings Extension

Extend the `combine_bundles` configuration to include the new feature flag:

```groovy
combine_bundles: [
    combine_asset                : 'CombinedShippingMPLevels',
    combine_reference_job        : 'CH1-SP-content-dev-first-patch.patchdata.start',
    is_target_branch            : true,
    source_branch_code          : 'CH1-SP-content-dev',
    source_branch_data          : 'CH1-SP-content-dev-first-patch',
    
    // NEW: Feature flag to control separate job creation
    use_separate_combined_job   : false,  // Default: false (backward compatibility)
    
    // NEW: Job labeling options for separate combined bundles job
    combined_job_label_type     : 'poolbuild',  // 'poolbuild' or 'dedicated'
    combined_job_platforms      : ['win64', 'ps5', 'xbsx'],  // Platforms to create jobs for
    
    // NEW: Optional dedicated machine label pattern
    dedicated_label_pattern     : '{stream_name} combine-bundles {platform}',
]
```

### 2. Feature Flag Behavior

#### When `use_separate_combined_job: false` (Default - Current Behavior)
- Combined bundles are created inline within frosty/patchfrosty jobs
- No separate Jenkins job is created
- Maintains full backward compatibility

#### When `use_separate_combined_job: true` (New Behavior)
- Separate Jenkins job(s) created for combined bundles creation
- Jobs run before any frosty/patchfrosty variants
- Frosty/patchfrosty jobs use `--use-precreated-combined-bundles` flag
- If combined bundles job fails, entire scheduler fails

### 3. Job Labeling Strategy

#### Poolbuild Label (Default)
```groovy
combined_job_label_type: 'poolbuild'
```
- Job label: `{poolbuild_label} {platform}`
- Example: `poolbuild win64`

#### Dedicated Machine Label
```groovy
combined_job_label_type: 'dedicated'
```
- Job label: `{stream_name} combine-bundles {platform}`
- Example: `CH1-content-dev-first-patch combine-bundles win64`

### 4. Platform Configuration

The `combined_job_platforms` array specifies which platforms need separate combined bundles jobs:

```groovy
combined_job_platforms: ['win64', 'ps5', 'xbsx']
```

This allows fine-grained control over which platforms use the separate job approach.

### 5. Job Naming Convention

Separate combined bundles jobs will follow this naming pattern:
```
{branch_name}.combined_bundles.{platform}
```

Examples:
- `CH1-content-dev-first-patch.combined_bundles.win64`
- `CH1-content-dev-first-patch.combined_bundles.ps5`
- `CH1-content-dev-first-patch.combined_bundles.xbsx`

## Implementation Strategy

### Phase 1: Infrastructure Setup
1. Create new Jenkins job type in `LibFrosty.groovy`
2. Add feature flag support to branch settings parsing
3. Update schedulers to conditionally create combined bundles jobs

### Phase 2: Script Integration
1. Add `--use-precreated-combined-bundles` support to `patch_frosty.py`
2. Update frosty/patchfrosty job parameter passing
3. Implement job dependency logic in schedulers

### Phase 3: Testing and Rollout
1. Test with feature flag disabled (current behavior)
2. Test with feature flag enabled on test branches
3. Gradual rollout to production branches

## Benefits

1. **Eliminates Duplication**: Combined bundles created once per platform instead of per config/format
2. **Improved Efficiency**: Reduces build time and resource usage
3. **Better Reliability**: Single point of failure for combined bundles creation
4. **Backward Compatibility**: Default behavior unchanged
5. **Flexible Configuration**: Per-branch and per-platform control

## Migration Path

1. **Default Off**: Feature flag defaults to `false` for all existing branches
2. **Opt-in Basis**: Branches can enable the feature when ready
3. **Gradual Migration**: Can be enabled per platform within a branch
4. **Easy Rollback**: Can be disabled quickly if issues arise

## Configuration Examples

### Example 1: Basic Separate Job (Poolbuild)
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
]
```

### Example 2: Dedicated Machines
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: true,
    combined_job_label_type: 'dedicated',
    combined_job_platforms: ['win64'],
]
```

### Example 3: Mixed Approach (Some Platforms Separate)
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: true,
    combined_job_platforms: ['win64'],  // Only win64 uses separate job
]
```

This design provides a robust, backward-compatible solution that addresses the requirements while allowing for flexible configuration and gradual adoption.
