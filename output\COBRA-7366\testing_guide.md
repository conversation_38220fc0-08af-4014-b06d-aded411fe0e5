# Testing Guide for Separate Combined Bundles Jobs Feature

## Overview
This document outlines the testing procedures for the separate combined bundles jobs feature (COBRA-7366). The testing ensures both backward compatibility and proper functionality of the new feature.

## Test Environment Setup

### Prerequisites
1. Access to a test branch with combined bundles configuration
2. Jenkins access to create and monitor jobs
3. Ability to modify branch settings files
4. Access to build machines with appropriate labels

### Test Branch Requirements
- Must have `combine_bundles` configuration in branch settings
- Should have both frosty and patchfrosty matrices with combine variants
- Must have access to source branches for combined bundles

## Test Scenarios

### Scenario 1: Backward Compatibility (Feature Disabled)

**Objective**: Verify that existing behavior is unchanged when feature is disabled.

**Configuration**:
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: false,  // or omit entirely
]
```

**Test Steps**:
1. Deploy branch settings with feature disabled
2. Trigger frosty.start job
3. Verify combined bundles are created inline within frosty jobs
4. Trigger patchfrosty.start job  
5. Verify combined bundles are created inline within patchfrosty jobs
6. Check build logs for inline combine operations
7. Verify no separate combined_bundles jobs are created

**Expected Results**:
- ✅ Frosty jobs create combined bundles inline
- ✅ Patchfrosty jobs create combined bundles inline
- ✅ No separate combined_bundles.* jobs exist
- ✅ Build times similar to baseline
- ✅ All artifacts deployed correctly

### Scenario 2: Basic Separate Jobs (Poolbuild)

**Objective**: Test basic separate combined bundles jobs with poolbuild labels.

**Configuration**:
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5'],
    combined_job_label_type: 'poolbuild',
]
```

**Test Steps**:
1. Deploy branch settings with feature enabled
2. Verify separate jobs are created:
   - `{branch}.combined_bundles.win64`
   - `{branch}.combined_bundles.ps5`
3. Trigger frosty.start job
4. Verify combined bundles jobs run first
5. Verify frosty jobs wait for combined bundles completion
6. Check that frosty jobs use `--use-precreated-combined-bundles`
7. Repeat for patchfrosty.start

**Expected Results**:
- ✅ Separate combined_bundles jobs created
- ✅ Jobs have correct poolbuild labels
- ✅ Combined bundles jobs run before frosty/patchfrosty
- ✅ Frosty jobs use pre-created bundles
- ✅ No duplication of combined bundles creation
- ✅ Build artifacts identical to inline approach

### Scenario 3: Dedicated Machine Labels

**Objective**: Test separate jobs with dedicated machine labels.

**Configuration**:
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: true,
    combined_job_platforms: ['win64'],
    combined_job_label_type: 'dedicated',
]
```

**Test Steps**:
1. Ensure dedicated machine exists with label: `{branch} combine-bundles win64`
2. Deploy branch settings
3. Verify job has correct dedicated label
4. Trigger scheduler and verify job runs on dedicated machine
5. Check job execution and artifact creation

**Expected Results**:
- ✅ Job uses dedicated machine label
- ✅ Job runs on correct dedicated machine
- ✅ Combined bundles created successfully
- ✅ Downstream jobs use pre-created bundles

### Scenario 4: Mixed Platform Configuration

**Objective**: Test configuration where only some platforms use separate jobs.

**Configuration**:
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: true,
    combined_job_platforms: ['win64'],  // Only win64
]
```

**Test Steps**:
1. Deploy configuration
2. Verify only win64 combined_bundles job is created
3. Trigger scheduler with multiple platforms (win64, ps5, xbsx)
4. Verify win64 uses separate job
5. Verify ps5/xbsx use inline creation
6. Check that both approaches work correctly

**Expected Results**:
- ✅ Only win64 combined_bundles job created
- ✅ Win64 frosty jobs use pre-created bundles
- ✅ PS5/XBSX frosty jobs create bundles inline
- ✅ All platforms produce correct artifacts

### Scenario 5: Failure Handling

**Objective**: Test scheduler behavior when combined bundles jobs fail.

**Test Steps**:
1. Configure separate combined bundles jobs
2. Introduce failure in combined bundles job (e.g., invalid changelist)
3. Trigger scheduler
4. Verify scheduler fails immediately
5. Verify frosty/patchfrosty jobs are not started
6. Check error reporting and logs

**Expected Results**:
- ✅ Scheduler fails when combined bundles job fails
- ✅ Downstream jobs are not executed
- ✅ Clear error messages in logs
- ✅ Build marked as failed with appropriate reason

### Scenario 6: Delta Bundles (Patchfrosty)

**Objective**: Test delta bundles creation in separate jobs for patchfrosty.

**Configuration**:
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: true,
    combined_job_platforms: ['win64'],
]
```

**Test Steps**:
1. Configure patchfrosty matrix with combine variants
2. Trigger patchfrosty.start
3. Verify combined bundles job creates both combined and delta bundles
4. Verify patchfrosty job uses both pre-created bundles
5. Check `--use-precreated-delta-bundles` flag is used
6. Verify delta bundles are correct

**Expected Results**:
- ✅ Combined bundles job creates delta bundles
- ✅ Patchfrosty uses pre-created delta bundles
- ✅ Delta bundles are functionally correct
- ✅ No duplication of delta bundle creation

## Validation Procedures

### Build Artifact Validation

1. **Bundle Comparison**:
   - Compare bundles created by separate jobs vs inline
   - Verify file sizes, checksums, and content
   - Ensure no functional differences

2. **Deployment Verification**:
   - Check network share deployment paths
   - Verify bundle metadata is correct
   - Confirm downstream jobs can fetch bundles

3. **Game Functionality**:
   - Deploy builds created with separate jobs
   - Test game functionality
   - Compare with builds from inline approach

### Performance Validation

1. **Build Time Comparison**:
   - Measure total scheduler execution time
   - Compare separate jobs vs inline approach
   - Document time savings from eliminated duplication

2. **Resource Usage**:
   - Monitor build machine utilization
   - Check parallel execution efficiency
   - Verify no resource contention issues

3. **Network Impact**:
   - Monitor network share usage
   - Check bundle upload/download patterns
   - Verify no excessive network traffic

### Log Analysis

1. **Job Execution Logs**:
   - Verify correct parameter passing
   - Check bundle creation commands
   - Confirm artifact deployment

2. **Scheduler Logs**:
   - Verify job dependency execution
   - Check failure handling
   - Confirm parameter injection

3. **Error Handling**:
   - Test various failure scenarios
   - Verify error messages are clear
   - Check rollback procedures

## Test Automation

### Automated Test Cases

Create automated tests for:
1. Branch settings parsing
2. Job creation logic
3. Parameter passing
4. Dependency execution order
5. Failure scenarios

### Continuous Integration

1. **Pre-commit Tests**:
   - Validate branch settings syntax
   - Check job configuration generation
   - Verify parameter compatibility

2. **Integration Tests**:
   - Test full scheduler execution
   - Validate artifact creation
   - Check downstream job integration

## Rollback Testing

### Rollback Scenarios

1. **Feature Disable**:
   - Change `use_separate_combined_job` to `false`
   - Verify immediate return to inline behavior
   - Check no orphaned jobs remain

2. **Platform Removal**:
   - Remove platform from `combined_job_platforms`
   - Verify job is no longer created
   - Check platform reverts to inline

3. **Emergency Rollback**:
   - Test rapid rollback during active builds
   - Verify in-progress builds complete correctly
   - Check new builds use inline approach

## Test Checklist

### Pre-Deployment Checklist
- [ ] All test scenarios pass
- [ ] Backward compatibility verified
- [ ] Performance impact measured
- [ ] Error handling tested
- [ ] Documentation updated
- [ ] Rollback procedures tested

### Post-Deployment Checklist
- [ ] Monitor first production builds
- [ ] Verify artifact quality
- [ ] Check build times
- [ ] Monitor error rates
- [ ] Validate downstream impacts
- [ ] Update team documentation

## Success Criteria

The implementation is considered successful when:

1. **Functionality**: All test scenarios pass without issues
2. **Compatibility**: Existing branches work unchanged when feature is disabled
3. **Performance**: Build times improve due to eliminated duplication
4. **Reliability**: Error handling works correctly in all failure scenarios
5. **Maintainability**: Configuration is clear and easy to understand
6. **Rollback**: Feature can be quickly disabled if issues arise

## Risk Mitigation

1. **Gradual Rollout**: Enable on test branches first, then production
2. **Monitoring**: Close monitoring during initial deployment
3. **Quick Rollback**: Ability to disable feature immediately
4. **Documentation**: Clear configuration and troubleshooting guides
5. **Support**: Team training on new feature and troubleshooting
