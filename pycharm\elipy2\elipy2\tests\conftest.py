"""
conftest.py

Auto importing fixtures
"""
import pytest
import re
import requests_mock
import time
from json import JSONDecodeError
from requests.models import Request
from typing import Any, Dict, List
from elipy2.tests.avalanche_web_api.utils import get_file_content


def body_contains_dict(expected: Dict[Any, Any]):
    def match(request: Request):
        nonlocal expected
        data = request.text
        res = False
        try:
            data = request.json()
            res = all(data.get(key, None) == val for key, val in expected.items())
        except JSONDecodeError as _:
            pass

        return res

    return match


def text_matches(expected: str):
    def match(request: Request):
        nonlocal expected
        data = request.text
        matcher = re.compile(expected, flags=re.IGNORECASE)
        res = matcher.match(data)
        return res is not None

    return match


def get_get_paths_and_data():
    paths_and_data = [
        {
            "path": r""".*/avalanche/status$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "server", "avalanche_status.json"),
        },
        {
            "path": r"""http://localhost_unavailable:1338/avalanche/status$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "server", "avalanche_status_unavailable.json"),
        },
        {
            "path": r"""http://localhost_redirect:1338/avalanche/status$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "server", "avalanche_status_unavailable.json"),
            "status_code": 304,
        },
        {
            "path": r"""http://test-url""",
            "method": "GET",
            "matcher": None,
            "text": '{ "text": "http://test-url" }',
        },
        {
            "path": r"""http://test-url/bad_error_code""",
            "method": "GET",
            "matcher": None,
            "text": '{ "text": "http://test-url/bad_error_code" }',
            "status_code": 201,
        },
        {
            "path": r"""http://localhost:1338/db$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "database", "db.json"),
        },
        {
            "path": r"""http://localhost_import_state:1338/db$""",
            "method": "GET",
            "matcher": None,
            "responses": [
                {"text": get_file_content("data", "database", "db_import_state_01.json")},
                {"text": get_file_content("data", "database", "db_import_state_02.json")},
            ],
        },
        {
            "path": r"""http://localhost_empty:1338/db$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "database", "db_empty.json"),
        },
        {
            "path": r"""http://localhost_baseline:1338/db$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "database", "db_baseline.json"),
        },
        {
            "path": r"""http://localhost_import:1338/db$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "database", "db_import.json"),
        },
        {
            "path": r""".*/db/BattlefieldGameData.kin-dev.Win32.Debug/kv/drone/builtlevels$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "database", "built_levels.json"),
        },
        {
            "path": r""".*/db/BattlefieldGameData.kin-dev.Win32.Debug/ops/chain$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "database", "ops_chain.json"),
        },
        {
            "path": r""".*/storage/pools/primary.json$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "storage", "pools_primary.json"),
        },
        {
            "path": r""".*/db/all$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "database", "db_all.json"),
        },
        {
            "path": r"""http://localhost:1338/cache/key$""",
            "method": "GET",
            "matcher": None,
            "text": '{"json": "data"}',
        },
        {
            "path": r"""http://localhost:1338/cache/key1/key2$""",
            "method": "GET",
            "matcher": None,
            "text": '{"json": "data"}',
        },
        {
            "path": r"""http://localhost:1338/cache/dre_buildfarm/test-url-reimport_d_branch_plt$""",
            "method": "GET",
            "matcher": None,
            "text": get_file_content("data", "cache", "reimport_needed.json"),
        },
    ]
    return paths_and_data


def get_post_paths_and_data():
    paths_and_data = []
    return paths_and_data


def get_put_paths_and_data():
    paths_and_data = [
        {
            "path": r"""http://localhost:1338/db/some_db/kv/drone/builtlevels$""",
            "method": "PUT",
            "matcher": None,
            "text": "",
            "status_code": 204,
        },
        {
            "path": r"""http://test-url/cache/testdata$""",
            "method": "PUT",
            "matcher": body_contains_dict({"data": "value"}),
            "text": "{}",
            "status_code": 204,
        },
        {
            "path": r"""http://test-url/cache/bad_status$""",
            "method": "PUT",
            "matcher": body_contains_dict({"data": "value"}),
            "text": "{}",
            "status_code": 300,
        },
        {
            "path": r"""http://test-url/cache/testdata$""",
            "method": "PUT",
            "matcher": text_matches("data"),
            "text": "{}",
            "status_code": 204,
        },
        {
            "path": r"""http://localhost:1338/cache/key1/key2$""",
            "method": "PUT",
            "matcher": text_matches("value"),
            "text": "{}",
            "status_code": 204,
        },
        {
            "path": r"""http://localhost:1338/cache/dre_buildfarm/test-url_d_branch_fake_platform$""",
            "method": "PUT",
            "matcher": body_contains_dict(
                {
                    "data_branch": "d_branch",
                    "data_changelist": "d_cl",
                    "code_changelist": "c_cl",
                    "platform": "fake_platform",
                }
            ),
            "text": "{}",
            "status_code": 204,
        },
    ]
    return paths_and_data


def get_delete_paths_and_data():
    paths_and_data = [
        {
            "path": r"""http://test-url/$""",
            "method": "DELETE",
            "matcher": None,
            "text": '{ "text": "http://test-url" }',
            "status_code": 200,
        },
    ]
    return paths_and_data


def get_paths_and_data() -> Dict[str, Any]:
    paths_and_data: List = []

    paths_and_data.extend(get_post_paths_and_data())
    paths_and_data.extend(get_get_paths_and_data())
    paths_and_data.extend(get_put_paths_and_data())
    paths_and_data.extend(get_delete_paths_and_data())

    return paths_and_data


@pytest.fixture(name="mock_avalanche")
def fixture_mock_avalanche():
    """
    Shared function for mocking avalanche
    """

    mock_r: requests_mock.Mocker
    with requests_mock.Mocker() as mock_r:
        paths_and_data = get_paths_and_data()

        for mock_data in paths_and_data:
            matcher = re.compile(mock_data["path"], flags=re.IGNORECASE)
            text = mock_data.get("text", "")
            status_code = mock_data.get("status_code", 200)
            responses = mock_data.get("responses", [])

            if not responses:
                responses = [{"text": text, "status_code": status_code}]

            mock_r.register_uri(
                mock_data.get("method", "GET"),
                matcher,
                additional_matcher=mock_data.get("matcher", None),
                response_list=responses,
            )

        yield mock_r


SLEEP_CALLS = {}


@pytest.fixture(autouse=True)
def sleepless(monkeypatch, request):
    def side_effect_func(seconds):
        count = SLEEP_CALLS.get(seconds, 0) + 1
        SLEEP_CALLS[seconds] = count

    if request.keywords.get("use_real_sleep", False) is False:
        monkeypatch.setattr(time, "sleep", side_effect_func)
