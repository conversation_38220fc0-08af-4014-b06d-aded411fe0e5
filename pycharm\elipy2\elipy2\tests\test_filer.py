"""
test_filer.py
"""

import os
import pytest
from mock import call, MagicMock, patch
from requests.exceptions import HTTPError
import elipy2
from elipy2 import filer
from elipy2.exceptions import ELIPYException, FbEnvCallException
from elipy2.frostbite import fbenv_layer
from elipy2.tests.testutils import got_but_expected


class TestFiler(object):
    def setup(self):
        from elipy2.filer import FilerUtils

        self.filer = FilerUtils()

        # mock core.robocopy
        self.patcher_robocopy = patch("elipy2.filer.core.robocopy")
        self.mock_robocopy = self.patcher_robocopy.start()

        self.patcher_bilbo = patch("elipy2.filer.core.use_bilbo")
        self.mock_use_bilbo = self.patcher_bilbo.start()

        self.patcher_bilbo_add = patch("elipy2.bilbo._BilboElasticSearch.add")
        self.mock_bilbo_add = self.patcher_bilbo_add.start()

        self.patcher_bilbo_used = patch(
            "elipy2.build_metadata.BuildMetadataManager.register_as_used"
        )
        self.mock_bilbo_used = self.patcher_bilbo_used.start()

        self.patcher_bilbo_tnt = patch(
            "elipy2.build_metadata.BuildMetadataManager.register_tnt_local_build"
        )
        self.mock_bilbo_tnt = self.patcher_bilbo_tnt.start()

        self.patcher_bilbo_code = patch(
            "elipy2.build_metadata.BuildMetadataManager.register_code_build"
        )
        self.mock_bilbo_code = self.patcher_bilbo_code.start()

        self.patcher_bilbo_register_frosty = patch(
            "elipy2.build_metadata.BuildMetadataManager.register_frosty_build"
        )
        self.mock_bilbo_register_frosty = self.patcher_bilbo_register_frosty.start()

        self.patcher_get_build_share_path = patch("elipy2.filer_paths.get_build_share_path")
        self.mock_get_build_share_path = self.patcher_get_build_share_path.start()
        self.mock_get_build_share_path.return_value = "test_buildshare"

        self.patcher_fb_version = patch("elipy2.frostbite_core.minimum_fb_version")
        self.mock_fb_version = self.patcher_fb_version.start()

        self.patcher_create_zip = patch("elipy2.core.create_zip")
        self.mock_create_zip = self.patcher_create_zip.start()

        self.patcher_remove = patch("os.remove")
        self.mock_remove = self.patcher_remove.start()

        self.patcher_extract_zip = patch("elipy2.core.extract_zip")
        self.mock_extract_zip = self.patcher_extract_zip.start()

        self.patcher_get_exe_name = patch("elipy2.frostbite.fbenv_layer.get_exe_name")
        self.mock_get_exe_name = self.patcher_get_exe_name.start()
        self.mock_get_exe_name.return_value = "exe_name"

        self.patcher_pullfrostbitebuild = patch("elipy2.frostbite.fbenv_layer.pullfrostbitebuild")
        self.mock_pullfrostbitebuild = self.patcher_pullfrostbitebuild.start()

        self.patcher_pushfrostbitebuild = patch("elipy2.frostbite.fbenv_layer.pushfrostbitebuild")
        self.mock_pushfrostbitebuild = self.patcher_pushfrostbitebuild.start()

        self.patcher_upload_metrics = patch("elipy2.telemetry.upload_metrics")
        self.mock_upload_metrics = self.patcher_upload_metrics.start()

    def teardown(self):
        patch.stopall()

    def test_deploy_code(self):
        self.mock_fb_version.return_value = False
        self.filer.deploy_code("main", "1234", "win64server", "final", use_fbenv_copy=False)
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_build_path("win64server", "final"),
            os.path.join(
                "test_buildshare", "Code", "main", "1234", "exe_name", "win64-server", "final"
            ),
        )
        assert self.mock_bilbo_code.call_count == 1

    def test_deploy_code_skip_bilbo(self):
        self.mock_fb_version.return_value = False
        self.filer.deploy_code(
            "main", "1234", "win64server", "final", use_fbenv_copy=False, skip_bilbo=True
        )
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_build_path("win64server", "final"),
            os.path.join(
                "test_buildshare", "Code", "main", "1234", "exe_name", "win64-server", "final"
            ),
        )
        assert self.mock_bilbo_code.call_count == 0

    def test_deploy_code_no_bilbo(self):
        self.mock_fb_version.return_value = False
        self.mock_use_bilbo.return_value = False
        self.filer.deploy_code("main", "1234", "win64server", "final", use_fbenv_copy=False)
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_build_path("win64server", "final"),
            os.path.join(
                "test_buildshare", "Code", "main", "1234", "exe_name", "win64-server", "final"
            ),
        )
        assert self.mock_bilbo_code.call_count == 0

    @patch("elipy2.filer.FilerUtils.deploy_tnt_local_build")
    def test_deploy_code_local(self, mock_local):
        self.mock_fb_version.return_value = False
        self.filer.deploy_code(
            "main",
            "1234",
            "win64server",
            "final",
            deploy_tnt_local_build=True,
            use_fbenv_copy=False,
        )
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_build_path("win64server", "final"),
            os.path.join(
                "test_buildshare", "Code", "main", "1234", "exe_name", "win64-server", "final"
            ),
        )
        assert mock_local.call_count == 1

    @patch("elipy2.os.path.exists")
    def test_deploy_code_already_deployed(self, mock_exists):
        self.mock_fb_version.return_value = False
        mock_exists.return_value = True
        with pytest.raises(ELIPYException):
            self.filer.deploy_code("main", "1234", "win64server", "final", use_fbenv_copy=False)

    def test_deploy_code_custom_source(self):
        self.mock_fb_version.return_value = False
        self.filer.deploy_code(
            "main", "1234", "win64server", "final", source="/some/path", use_fbenv_copy=False
        )
        self.mock_robocopy.assert_called_once_with(
            "/some/path",
            os.path.join(
                "test_buildshare", "Code", "main", "1234", "exe_name", "win64-server", "final"
            ),
        )

    def test_deploy_code_with_tnt(self):
        self.mock_fb_version.return_value = False
        self.filer.deploy_code("main", "1234", "win64server", "final", deploy_tnt=True, source=None)
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_build_path("win64server", "final"),
            os.path.join(
                "test_buildshare", "Code", "main", "1234", "exe_name", "win64-server", "final"
            ),
        )

    def test_deploy_code_with_tnt_overwrite(self):
        self.mock_fb_version.return_value = False
        self.filer.deploy_code(
            "main", "1234", "win64server", "final", deploy_tnt=True, source=None, overwrite=True
        )
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_build_path("win64server", "final"),
            os.path.join(
                "test_buildshare", "Code", "main", "1234", "exe_name", "win64-server", "final"
            ),
        )

    def test_deploy_code_tool(self):
        self.mock_fb_version.return_value = False
        self.filer.deploy_code("main", "1234", "tool", "release", use_fbenv_copy=False)
        self.mock_robocopy.assert_has_calls(
            [
                call(
                    elipy2.local_paths.get_local_build_path("pipeline", "release"),
                    elipy2.filer_paths.get_code_build_path(
                        "main", "1234", "pipeline", "release", nomaster=False
                    ),
                ),
                call(
                    elipy2.local_paths.get_local_build_path("frosted", "release"),
                    elipy2.filer_paths.get_code_build_path(
                        "main", "1234", "frosted", "release", nomaster=False
                    ),
                ),
                call(
                    elipy2.local_paths.get_local_build_path("win64-dll", "release"),
                    elipy2.filer_paths.get_code_build_path(
                        "main", "1234", "win64-dll", "release", nomaster=False
                    ),
                ),
            ]
        )
        assert self.mock_robocopy.call_count == 3
        assert self.mock_bilbo_code.call_count == 1

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_fb2019_with_tnt(self, mock_exists, mock_code_path):
        mock_code_path.return_value = "path"
        self.mock_fb_version.return_value = True
        mock_exists.return_value = False
        self.filer.deploy_code(
            "main",
            "1234",
            "win64server",
            "final",
            deploy_tnt=True,
            source=None,
            deploy_houdini=True,
        )
        self.mock_robocopy.assert_called_once_with(
            os.path.join("tnt_root", "bin", "procedural"),
            os.path.join("test_buildshare", "Code", "main", "1234", "procedural"),
        )
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact=fbenv_layer.get_exe_name(),
            platforms=["win64server"],
            variant="final",
            remote_dir=os.path.join("test_buildshare", "Code", "main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=[],
        )

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_artifact_fb2019_with_tnt(self, mock_exists, mock_code_path):
        mock_code_path.return_value = "path"
        self.mock_fb_version.return_value = True
        mock_exists.return_value = False
        self.filer.deploy_code(
            "main", "1234", "win64server", "final", "examplegame", deploy_tnt=True, source=None
        )
        assert self.mock_robocopy.call_count == 0
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64server"],
            variant="final",
            remote_dir=os.path.join("test_buildshare", "Code", "main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=[],
        )

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_artifact_fb2019_tool(self, mock_exists, mock_code_path):
        mock_code_path.return_value = "path"
        self.mock_fb_version.return_value = True
        mock_exists.return_value = False
        self.filer.deploy_code("main", "1234", "tool", "release", "examplegame")
        assert self.mock_robocopy.call_count == 0
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["pipeline", "frosted", "win64-dll"],
            variant="release",
            remote_dir=os.path.join("test_buildshare", "Code", "main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=[],
        )

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_custom_binaries_destination(self, mock_exists, mock_code_path):
        mock_code_path.return_value = "path"
        self.mock_fb_version.return_value = True
        mock_exists.return_value = False
        self.filer.deploy_code(
            "main",
            "1234",
            "tool",
            "release",
            "examplegame",
            custom_binaries_destination="custom_binaries_destination",
        )
        assert self.mock_robocopy.call_count == 0
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["pipeline", "frosted", "win64-dll"],
            variant="release",
            remote_dir="custom_binaries_destination",
            mirror=True,
            label="1234",
            copy_build_args=[],
        )

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_tool_tool_targets_override(self, mock_exists, mock_code_path):
        mock_code_path.return_value = "path"
        self.mock_fb_version.return_value = True
        mock_exists.return_value = False
        tool_targets = ["my", "test", "platforms"]
        # make sure platform is overriden when tool is specified
        self.filer.deploy_code(
            "main", "1234", "tool", "release", "examplegame", tool_targets=tool_targets
        )
        assert self.mock_robocopy.call_count == 0
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=tool_targets,
            variant="release",
            remote_dir=os.path.join("test_buildshare", "Code", "main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=[],
        )

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_platform_tool_targets_override(self, mock_exists, mock_code_path):
        mock_code_path.return_value = "path"
        self.mock_fb_version.return_value = True
        mock_exists.return_value = False
        tool_targets = ["my", "test", "platforms"]
        # deploy another platform other than "tool", make sure the tool_targets isn't used.
        self.filer.deploy_code(
            "main", "1234", "ps4", "release", "examplegame", tool_targets=tool_targets
        )
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["ps4"],
            variant="release",
            remote_dir=os.path.join("test_buildshare", "Code", "main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=[],
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_deploy_code_use_fbcli_no_licensee(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = []
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        platform = "ps5"
        branch = "dev-na"
        changelist = "123345"
        config = "retail"
        self.filer.deploy_code(branch, changelist, platform, config, deploy_tests=True)
        assert 2 == len(self.mock_pushfrostbitebuild.call_args_list)
        self.mock_pushfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=self.mock_get_exe_name.return_value,
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_deploy_code_use_fbcli_multi_licensee(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["ExampleGame", "FBNullLicensee"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        platform = "ps5"
        branch = "dev-na"
        changelist = "123345"
        config = "retail"
        self.filer.deploy_code(branch, changelist, platform, config, deploy_tests=True)
        assert 3 == len(self.mock_pushfrostbitebuild.call_args_list)
        self.mock_pushfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=mock_get_enabled_licensees.return_value[0],
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact=mock_get_enabled_licensees.return_value[1],
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    mirror=False,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_deploy_code_use_fbcli_single_licensee(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["GW3"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        platform = "win64"
        branch = "dev-na-staging"
        changelist = "123345"
        config = "final"
        self.filer.deploy_code(branch, changelist, platform, config, deploy_tests=True)
        assert 2 == len(self.mock_pushfrostbitebuild.call_args_list)
        self.mock_pushfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=mock_get_enabled_licensees.return_value[0],
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_deploy_code_not_use_fbcli_custom_tag(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["GW3"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = False
        platform = "android"
        branch = "dev-na"
        changelist = "123345"
        config = "debug"
        custom_tag = "test_folder"
        self.filer.deploy_code(
            branch, changelist, platform, config, deploy_tests=True, custom_tag=custom_tag
        )
        assert 2 == len(self.mock_pushfrostbitebuild.call_args_list)
        self.mock_pushfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=mock_get_enabled_licensees.return_value[0],
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value,
                        "Code",
                        branch,
                        custom_tag,
                        changelist,
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value,
                        "Code",
                        branch,
                        custom_tag,
                        changelist,
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_deploy_code_use_fbcli_custom_tag(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["GW3"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        platform = "android"
        branch = "dev-na"
        changelist = "123345"
        config = "debug"
        custom_tag = "test_folder"
        self.filer.deploy_code(
            branch, changelist, platform, config, deploy_tests=True, custom_tag=custom_tag
        )
        assert 2 == len(self.mock_pushfrostbitebuild.call_args_list)
        self.mock_pushfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=mock_get_enabled_licensees.return_value[0],
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch, custom_tag
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch, custom_tag
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_deploy_code_mirror_false(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["GW3"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        platform = "android"
        branch = "dev-na"
        changelist = "123345"
        config = "debug"
        self.filer.deploy_code(
            branch, changelist, platform, config, deploy_tests=True, mirror=False
        )
        assert 2 == len(self.mock_pushfrostbitebuild.call_args_list)
        self.mock_pushfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=mock_get_enabled_licensees.return_value[0],
                    platforms=[platform],
                    variant=config,
                    mirror=False,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_deploy_code_use_fbcli_artifact_override(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["GW3"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        override_artifact = "my_override_artifact"
        platform = "win64"
        branch = "dev-na-staging"
        changelist = "123345"
        config = "final"
        self.filer.deploy_code(
            branch, changelist, platform, config, artifact_name=override_artifact, deploy_tests=True
        )
        assert 2 == len(self.mock_pushfrostbitebuild.call_args_list)
        self.mock_pushfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=override_artifact,
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_fetch_code_use_fbcli_no_licensee(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = []
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        platform = "win64server"
        branch = "dev-na"
        changelist = "123345"
        config = "retail"
        self.filer.fetch_code(branch, changelist, platform, config, fetch_tests=True)
        assert 2 == len(self.mock_pullfrostbitebuild.call_args_list)
        self.mock_pullfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=self.mock_get_exe_name.return_value,
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_fetch_code_use_fbcli_multi_licensee(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["ExampleGame", "FBNullLicensee"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        platform = "win64game"
        branch = "dev-na"
        changelist = "123345"
        config = "debug"
        self.filer.fetch_code(branch, changelist, platform, config, fetch_tests=True)
        assert 3 == len(self.mock_pullfrostbitebuild.call_args_list)
        self.mock_pullfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=mock_get_enabled_licensees.return_value[0],
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact=mock_get_enabled_licensees.return_value[1],
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    mirror=False,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_fetch_code_use_fbcli_single_licensee(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["GW3"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        platform = "android"
        branch = "dev-na"
        changelist = "123345"
        config = "debug"
        self.filer.fetch_code(branch, changelist, platform, config, fetch_tests=True)
        assert 2 == len(self.mock_pullfrostbitebuild.call_args_list)
        self.mock_pullfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=mock_get_enabled_licensees.return_value[0],
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_fetch_code_not_use_fbcli_custom_tag(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["GW3"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = False
        platform = "android"
        branch = "dev-na"
        changelist = "123345"
        config = "debug"
        custom_tag = "test_folder"
        self.filer.fetch_code(
            branch, changelist, platform, config, fetch_tests=True, custom_tag=custom_tag
        )
        assert 2 == len(self.mock_pullfrostbitebuild.call_args_list)
        self.mock_pullfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=mock_get_enabled_licensees.return_value[0],
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value,
                        "Code",
                        branch,
                        custom_tag,
                        changelist,
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value,
                        "Code",
                        branch,
                        custom_tag,
                        changelist,
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_fetch_code_use_fbcli_custom_tag(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["GW3"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        platform = "android"
        branch = "dev-na"
        changelist = "123345"
        config = "debug"
        custom_tag = "test_folder"
        self.filer.fetch_code(
            branch, changelist, platform, config, fetch_tests=True, custom_tag=custom_tag
        )
        assert 2 == len(self.mock_pullfrostbitebuild.call_args_list)
        self.mock_pullfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=mock_get_enabled_licensees.return_value[0],
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch, custom_tag
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch, custom_tag
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.frostbite.fbenv_layer.use_fbcli")
    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.frostbite.fbenv_layer.get_enabled_licensees")
    def test_fetch_code_use_fbcli_artifact_override(
        self, mock_get_enabled_licensees, mock_get_platform_path, mock_use_fbcli
    ):
        mock_get_enabled_licensees.return_value = ["GW3"]
        mock_get_platform_path.return_value = "path"
        mock_use_fbcli.return_value = True
        override_artifact = "my_override_artifact"
        platform = "win64"
        branch = "dev-na-staging"
        changelist = "123345"
        config = "final"
        self.filer.fetch_code(
            branch, changelist, platform, config, artifact_name=override_artifact, fetch_tests=True
        )
        assert 2 == len(self.mock_pullfrostbitebuild.call_args_list)
        self.mock_pullfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=override_artifact,
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    mirror=True,
                    label=changelist,
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=[platform],
                    variant=config,
                    remote_dir=os.path.join(
                        self.mock_get_build_share_path.return_value, "Code", branch
                    ),
                    label=changelist,
                    copy_build_args=[],
                ),
            ]
        )

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_artifact_fb2019_tool_deploy_frostedtests(
        self, mock_exists, mock_code_path
    ):
        mock_code_path.return_value = "path"
        self.mock_fb_version.return_value = True
        mock_exists.return_value = False
        self.filer.deploy_code(
            "main", "1234", "tool", "release", "examplegame", deploy_frostedtests=True
        )
        assert self.mock_robocopy.call_count == 0
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["pipeline", "frosted", "win64-dll", "frostedtests"],
            variant="release",
            remote_dir=os.path.join("test_buildshare", "Code", "main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=[],
        )

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_fb2019_with_tnt_use_fbenv_copy(self, mock_exists, mock_code_path):
        mock_code_path.return_value = "path"
        self.mock_fb_version.return_value = True
        mock_exists.return_value = False
        self.filer.deploy_code(
            "main",
            "1234",
            "win64server",
            "final",
            deploy_tnt=True,
            source=None,
            use_fbenv_copy=True,
        )
        assert self.mock_robocopy.call_count == 0
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact=fbenv_layer.get_exe_name(),
            platforms=["win64server"],
            variant="final",
            remote_dir=os.path.join("test_buildshare", "Code", "main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=[],
        )

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_fb2019_with_fbenv_args(self, mock_exists, mock_code_path):
        mock_code_path.return_value = "path"
        mock_exists.return_value = False
        self.mock_fb_version.return_value = True
        self.filer.deploy_code(
            "main",
            "1234",
            "win64server",
            "final",
            source=None,
            use_fbenv_copy=True,
            fbenv_copy_args=["/XF *.pdb"],
        )
        assert self.mock_robocopy.call_count == 0
        self.mock_pushfrostbitebuild.assert_called_once_with(
            artifact=fbenv_layer.get_exe_name(),
            platforms=["win64server"],
            variant="final",
            remote_dir=os.path.join("test_buildshare", "Code", "main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=["/XF *.pdb"],
        )

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_fb2019_with_fbenv_args_with_tests(self, mock_exists, mock_code_path):
        mock_code_path.return_value = "path"
        mock_exists.return_value = False
        self.mock_fb_version.return_value = True
        self.filer.deploy_code(
            "main",
            "1234",
            "win64server",
            "final",
            source=None,
            use_fbenv_copy=True,
            fbenv_copy_args=["/XF *.pdb"],
            deploy_tests=True,
        )
        assert self.mock_robocopy.call_count == 0
        self.mock_pushfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=fbenv_layer.get_exe_name(),
                    platforms=["win64server"],
                    variant="final",
                    remote_dir=os.path.join("test_buildshare", "Code", "main", "1234"),
                    mirror=True,
                    label="1234",
                    copy_build_args=["/XF *.pdb"],
                ),
                call(
                    artifact="tests",
                    platforms=["win64server"],
                    variant="final",
                    remote_dir=os.path.join("test_buildshare", "Code", "main", "1234"),
                    label="1234",
                    copy_build_args=["/XF *.pdb"],
                ),
            ]
        )
        assert self.mock_pushfrostbitebuild.call_count == 2

    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_fb2019_with_fbenv_args_overwrite_check(self, mock_exists, mock_code_path):
        mock_code_path.return_value = "path"
        mock_exists.return_value = True
        self.mock_fb_version.return_value = True
        with pytest.raises(ELIPYException):
            self.filer.deploy_code(
                "main",
                "1234",
                "win64server",
                "final",
                source=None,
                use_fbenv_copy=True,
                fbenv_copy_args=["/XF *.pdb"],
            )
        assert self.mock_robocopy.call_count == 0
        assert self.mock_pushfrostbitebuild.call_count == 0

    @patch("time.sleep")
    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_fb2019_file_issue(self, mock_exists, mock_code_path, mock_sleep):
        mock_code_path.return_value = "path"
        mock_exists.return_value = False
        self.mock_fb_version.return_value = True
        self.mock_pushfrostbitebuild.side_effect = FbEnvCallException()
        with pytest.raises(FbEnvCallException):
            self.filer.deploy_code(
                "main",
                "1234",
                "win64server",
                "final",
                source=None,
                use_fbenv_copy=True,
                fbenv_copy_args=["/XF *.pdb"],
            )
        assert self.mock_robocopy.call_count == 0
        assert self.mock_pushfrostbitebuild.call_count == 2

    @patch("time.sleep")
    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_fb2019_file_issue(self, mock_exists, mock_code_path, mock_sleep):
        mock_code_path.return_value = "path"
        mock_exists.return_value = False
        self.mock_fb_version.return_value = True
        self.mock_pushfrostbitebuild.side_effect = [FbEnvCallException(), None]
        self.filer.deploy_code(
            "main",
            "1234",
            "win64server",
            "final",
            source=None,
            use_fbenv_copy=True,
            fbenv_copy_args=["/XF *.pdb"],
        )
        assert self.mock_robocopy.call_count == 0
        assert self.mock_pushfrostbitebuild.call_count == 2

    @patch("time.sleep")
    @patch("elipy2.filer_paths.get_code_build_path")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_code_fb2019_deploy_tests_file_issue(
        self, mock_exists, mock_code_path, mock_sleep
    ):
        mock_code_path.return_value = "path"
        mock_exists.return_value = False
        self.mock_fb_version.return_value = True
        self.mock_pushfrostbitebuild.side_effect = [
            None,
            FbEnvCallException(),
            None,
            FbEnvCallException(),
        ]
        with pytest.raises(FbEnvCallException):
            self.filer.deploy_code(
                "main",
                "1234",
                "win64server",
                "final",
                source=None,
                use_fbenv_copy=True,
                fbenv_copy_args=["/XF *.pdb"],
                deploy_tests=True,
            )
        assert self.mock_robocopy.call_count == 0
        assert self.mock_pushfrostbitebuild.call_count == 4

    @patch("elipy2.code.CodeUtils.clean_local")
    @patch("os.path.isfile")
    @patch("os.path.isdir")
    def test_import_tnt_local_build(self, mock_isdir, mock_isfile, mock_clean):
        mock_isfile.return_value = False
        mock_isdir.return_value = True
        self.filer.import_tnt_local_build("main", "1234", "win64game", "release")
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                elipy2.filer_paths.get_tnt_local_build_root_path("main", "1234"),
                "win64game",
                "release",
            ),
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "Local"),
            purge=True,
            quiet=True,
        )

    @patch("elipy2.code.CodeUtils.clean_local")
    @patch("os.path.isfile")
    @patch("os.path.isdir")
    def test_import_tnt_local_build_zip(self, mock_isdir, mock_isfile, mock_clean):
        dest = os.path.join("tnt_root", "Local")
        source = os.path.join("tnt_root", "Local", "state.zip")
        mock_isfile.return_value = True
        mock_isdir.return_value = True
        self.filer.import_tnt_local_build("main", "1234", "win64game", "release")
        self.mock_extract_zip.assert_called_once_with(destination=dest, source=source)

    @patch("elipy2.code.CodeUtils.clean_local")
    @patch("os.path.isfile")
    @patch("elipy2.build_metadata.BuildMetadataManager.get_last_successful")
    @patch("os.path.isdir")
    def test_import_tnt_local_build_plt(self, mock_isdir, mock_bilbo, mock_isfile, mock_clean):
        mock_isfile.return_value = False
        mock_isdir.return_value = True
        mock_bilbo.return_value = {"changelist": "123"}
        self.filer.import_tnt_local_build("main", platform="win64game", config="release")
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                elipy2.filer_paths.get_tnt_local_build_root_path("main", "123"),
                "win64game",
                "release",
            ),
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "Local"),
            purge=True,
            quiet=True,
        )

    @patch("elipy2.code.CodeUtils.clean_local")
    @patch("os.path.isfile")
    @patch("elipy2.build_metadata.BuildMetadataManager.get_last_successful")
    @patch("os.path.isdir")
    def test_import_tnt_local_build_plt_nomaster(
        self, mock_isdir, mock_bilbo, mock_isfile, mock_clean
    ):
        mock_isfile.return_value = False
        mock_isdir.return_value = True
        mock_bilbo.return_value = {"changelist": "123"}
        self.filer.import_tnt_local_build(
            "main", platform="win64game", config="release", nomaster=True
        )
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                elipy2.filer_paths.get_tnt_local_build_root_path("main", "123"),
                "win64game_nomaster",
                "release",
            ),
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "Local"),
            purge=True,
            quiet=True,
        )

    @patch("elipy2.code.CodeUtils.clean_local")
    @patch("os.path.isfile")
    @patch("elipy2.build_metadata.BuildMetadataManager.get_last_successful")
    @patch("os.path.isdir")
    def test_import_tnt_local_build_plt_nobilbo(
        self, mock_isdir, mock_bilbo, mock_isfile, mock_clean
    ):
        mock_isfile.return_value = False
        self.mock_use_bilbo.return_value = False
        mock_isdir.return_value = True
        mock_bilbo.return_value = {"changelist": "123"}
        self.filer.import_tnt_local_build(
            "main", platform="win64game", config="release", nomaster=True, changelist="123"
        )
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                elipy2.filer_paths.get_tnt_local_build_root_path("main", "123"),
                "win64game_nomaster",
                "release",
            ),
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "Local"),
            purge=True,
            quiet=True,
        )

    @patch("os.path.isfile")
    @patch("elipy2.code.CodeUtils.clean_local")
    @patch("elipy2.build_metadata.BuildMetadataManager.get_last_successful")
    @patch("os.path.isdir")
    def test_import_tnt_local_build_plt_no_cl(
        self, mock_isdir, mock_bilbo, mock_clean, mock_isfile
    ):
        mock_isfile.return_value = False
        mock_isdir.return_value = True
        mock_bilbo.return_value = {"chanst": "123"}
        self.filer.import_tnt_local_build("main", platform="win64game", config="release")
        assert self.mock_robocopy.call_count == 0
        assert mock_clean.call_count == 1

    @patch("os.path.isfile")
    @patch("elipy2.code.CodeUtils.clean_local")
    @patch("os.path.isdir")
    def test_import_tnt_local_build_not_there(self, mock_isdir, mock_clean, mock_isfile):
        mock_isfile.return_value = False
        mock_isdir.return_value = False
        self.filer.import_tnt_local_build("main", "1234", "win64game", "release")
        assert self.mock_robocopy.call_count == 0
        assert mock_clean.call_count == 1

    @patch("elipy2.code.CodeUtils.clean_local")
    @patch("elipy2.build_metadata.BuildMetadataManager.get_last_successful")
    def test_import_tnt_local_build_http_error(self, mock_get_last_successful, mock_clean_local):
        self.mock_use_bilbo.return_value = True
        mock_get_last_successful.side_effect = HTTPError()
        assert self.filer.import_tnt_local_build("main", None, "win64game", "release") == ""
        mock_clean_local.assert_called_once_with(close_handles=True)

    @patch("elipy2.build_metadata.BuildMetadataManager.register_ant_local_build")
    def test_deploy_ant_local_build(self, mock_register):
        self.filer.deploy_ant_local_build("main", "1234")
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_ant_local(),
            os.path.join("test_buildshare", "ant_cache", "main", "1234"),
            extra_args=["/XD", "Assets", "Raw", "Resources", "Type"],
            quiet=True,
        )
        assert mock_register.call_count == 1

    @patch("elipy2.build_metadata.BuildMetadataManager.register_ant_local_build")
    def test_deploy_ant_local_build_no_bilbo(self, mock_register):
        self.mock_use_bilbo.return_value = False
        self.filer.deploy_ant_local_build("main", "1234")
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_ant_local(),
            os.path.join("test_buildshare", "ant_cache", "main", "1234"),
            extra_args=["/XD", "Assets", "Raw", "Resources", "Type"],
            quiet=True,
        )
        assert mock_register.call_count == 0

    @patch("elipy2.build_metadata.BuildMetadataManager.register_ant_local_build")
    def test_deploy_ant_local_build_exception(self, mock_register):
        self.mock_robocopy.side_effect = Exception()
        self.filer.deploy_ant_local_build("main", "1234")
        assert mock_register.call_count == 0

    def test_deploy_tnt_local_build(self):
        self.filer.deploy_tnt_local_build("main", "1234", "ps4", "final", use_zip=False)
        self.mock_robocopy.assert_called_once_with(
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "Local"),
            os.path.join("test_buildshare", "tnt_local", "main", "1234", "ps4", "final"),
            extra_args=[
                "/XD",
                "Bin",
                "Frosty",
                "baseline_state",
                "current_delta",
                "ps4_live_package",
                "ps4_disk_package",
            ],
            quiet=True,
        )
        assert self.mock_bilbo_tnt.call_count == 1

    def test_deploy_tnt_local_build_nomaster(self):
        self.mock_use_bilbo.return_value = False
        _filer = elipy2.filer.FilerUtils()
        _filer.deploy_tnt_local_build("main", "1234", "ps4", "final", nomaster=True, use_zip=False)
        self.mock_robocopy.assert_called_once_with(
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "Local"),
            os.path.join("test_buildshare", "tnt_local", "main", "1234", "ps4_nomaster", "final"),
            extra_args=[
                "/XD",
                "Bin",
                "Frosty",
                "baseline_state",
                "current_delta",
                "ps4_live_package",
                "ps4_disk_package",
            ],
            quiet=True,
        )
        assert self.mock_bilbo_tnt.call_count == 0

    def test_deploy_tnt_local_build_zip(self):
        source = os.path.join("tnt_root", "Local", "Build")
        dest = os.path.join("tnt_root", "Local", "state.zip")

        self.filer.deploy_tnt_local_build("main", "1234", "ps4", "final", use_zip=True)
        self.mock_create_zip.assert_called_once_with(
            additional_args=["-r", os.path.join("tnt_root", "Local", "*_*_*.sln"), ">", "nul"],
            compression=0,
            destination=dest,
            keep_dir_source=True,
            source=source,
        )
        assert self.mock_bilbo_tnt.call_count == 1

    def test_deploy_tnt_local_build_zip_failure(self):
        self.mock_create_zip.side_effect = Exception()
        self.filer.deploy_tnt_local_build("main", "1234", "ps4", "final", use_zip=True)
        assert self.mock_robocopy.call_count == 0

    def test_deploy_tnt_local_build_failure(self):
        self.mock_robocopy.side_effect = Exception()
        self.filer.deploy_tnt_local_build("main", "1234", "ps4", "final", use_zip=False)

    @patch("os.path.isdir")
    def test_import_ant_local_build(self, mock_isdir):
        mock_isdir.return_value = True
        self.filer.import_ant_local_build("main", "1234")
        self.mock_robocopy.assert_called_once_with(
            elipy2.filer_paths.get_ant_local_build_path("main", "1234"),
            elipy2.local_paths.get_ant_local(),
            purge=True,
            quiet=True,
        )

    @patch("elipy2.build_metadata.BuildMetadataManager.get_last_successful")
    @patch("os.path.isdir")
    def test_import_ant_local_build_plt(self, mock_isdir, mock_bilbo):
        mock_isdir.return_value = True
        mock_bilbo.return_value = {"changelist": "123"}
        self.filer.import_ant_local_build("main", platform="ps4")
        self.mock_robocopy.assert_called_once_with(
            elipy2.filer_paths.get_ant_local_build_path("main", "123"),
            elipy2.local_paths.get_ant_local(),
            purge=True,
            quiet=True,
        )

    @patch("elipy2.data.DataUtils.clean_ant_local")
    @patch("elipy2.build_metadata.BuildMetadataManager.get_last_successful")
    @patch("os.path.isdir")
    def test_import_ant_local_build_plt_no_cl(self, mock_isdir, mock_bilbo, mock_clean):
        mock_isdir.return_value = True
        mock_bilbo.return_value = {"sfbo": "123"}
        self.filer.import_ant_local_build("main", platform="ps4")
        assert self.mock_robocopy.call_count == 0
        assert mock_clean.call_count == 1

    @patch("elipy2.data.DataUtils.clean_ant_local")
    @patch("os.path.isdir")
    def test_import_ant_local_build_no_dir(self, mock_isdir, mock_clean):
        mock_isdir.return_value = False
        self.filer.import_ant_local_build("main", "1234")
        assert self.mock_robocopy.call_count == 0
        assert mock_clean.call_count == 1

    @patch("elipy2.build_metadata.BuildMetadataManager.register_avalanche_state")
    def test_deploy_avalanche_state(self, mock_bilbo):
        self.filer.deploy_avalanche_state("win64", "some-branch", "123", "321")
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_avalanche_export_path(
                "some-branch", "123", "321", "win64"
            ),
            os.path.join("test_buildshare", "AvalancheState", "some-branch", "win64", "123", "321"),
            quiet=True,
        )
        assert mock_bilbo.call_count == 1

    @patch("elipy2.build_metadata.BuildMetadataManager.register_avalanche_state")
    def test_deploy_avalanche_state_no_bilbo(self, mock_bilbo):
        self.mock_use_bilbo.return_value = False
        self.filer.deploy_avalanche_state("win64", "some-branch", "123", "321")
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_avalanche_export_path(
                "some-branch", "123", "321", "win64"
            ),
            os.path.join("test_buildshare", "AvalancheState", "some-branch", "win64", "123", "321"),
            quiet=True,
        )
        assert mock_bilbo.call_count == 0

    @patch("os.path.exists")
    def test_fetch_avalanche_state(self, mock_isdir):
        mock_isdir.return_value = True
        self.filer.fetch_avalanche_state("win64", "some-branch", "123", "321")
        self.mock_robocopy.assert_called_once_with(
            elipy2.filer_paths.get_avalanche_export_path("some-branch", "win64", "123", "321"),
            elipy2.local_paths.get_local_avalanche_export_path(
                "some-branch", "123", "321", "win64"
            ),
            quiet=True,
        )

    @patch("os.path.exists")
    def test_fetch_avalanche_state_no_dir(self, mock_isdir):
        mock_isdir.return_value = False
        with pytest.raises(ELIPYException):
            self.filer.fetch_avalanche_state("win64", "some-branch", "123", "321")
            assert self.mock_robocopy.call_count == 0

    @patch("elipy2.build_metadata.BuildMetadataManager.register_as_used")
    @patch("elipy2.core.use_bilbo")
    @patch("os.path.exists")
    def test_fetch_avalanche_state_no_bilbo(
        self, mock_isdir, mock_use_bilbo, mock_register_as_used
    ):
        mock_isdir.return_value = True
        mock_use_bilbo.return_value = False
        self.filer.fetch_avalanche_state("win64", "some-branch", "123", "321")
        assert mock_register_as_used.call_count == 0

    @patch("elipy2.build_metadata.BuildMetadataManager.register_as_used")
    @patch("elipy2.core.use_bilbo")
    @patch("os.path.exists")
    def test_fetch_avalanche_state_use_bilbo(
        self, mock_isdir, mock_use_bilbo, mock_register_as_used
    ):
        mock_isdir.return_value = True
        mock_use_bilbo.return_value = True
        self.filer.fetch_avalanche_state("win64", "some-branch", "123", "321")
        mock_register_as_used.assert_called_once_with(
            elipy2.filer_paths.get_avalanche_export_path("some-branch", "win64", "123", "321")
        )

    @patch("elipy2.local_paths.get_platform_path")
    def test_fetch_code(self, mock_path):
        self.mock_fb_version.return_value = False
        mock_path.return_value = "path"
        self.filer.fetch_code("main", "1234", "win64game", "release")
        self.mock_robocopy.assert_called_once_with(
            os.path.join("test_buildshare", "Code", "main", "1234", "path"),
            os.path.join(os.environ["TNT_ROOT"], "Local", "Bin", "path"),
            purge=True,
            extra_args=[],
        )

    @patch("elipy2.local_paths.get_platform_path")
    def test_fetch_code_no_bilbo(self, mock_path):
        self.mock_fb_version.return_value = False
        self.mock_use_bilbo.return_value = False
        mock_path.return_value = "path"
        self.filer.fetch_code(
            "main", "1234", "win64game", "release", use_fbenv_copy=False, exclude_pdb_files=True
        )
        self.mock_robocopy.assert_called_once_with(
            os.path.join("test_buildshare", "Code", "main", "1234", "path"),
            os.path.join(os.environ["TNT_ROOT"], "Local", "Bin", "path"),
            purge=True,
            extra_args=["/XF", "*.pdb"],
        )

    @patch("elipy2.local_paths.get_platform_path")
    def test_fetch_code_fb2019_no_bilbo(self, mock_path):
        self.mock_fb_version.return_value = True
        self.mock_use_bilbo.return_value = False
        mock_path.return_value = "path"
        self.filer.fetch_code("main", "1234", "win64game", "release", use_fbenv_copy=True)
        assert self.mock_robocopy.call_count == 0
        self.mock_pullfrostbitebuild.assert_called_once_with(
            artifact=fbenv_layer.get_exe_name(),
            platforms=["win64game"],
            variant="release",
            remote_dir=elipy2.filer_paths.get_code_build_root_path("main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=[],
        )

    @patch("elipy2.local_paths.get_platform_path")
    def test_fetch_code_artifact_fb2019_no_bilbo(self, mock_path):
        self.mock_fb_version.return_value = True
        self.mock_use_bilbo.return_value = False
        mock_path.return_value = "path"
        self.filer.fetch_code(
            "main", "1234", "win64game", "release", "examplegame", use_fbenv_copy=True
        )
        assert self.mock_robocopy.call_count == 0
        self.mock_pullfrostbitebuild.assert_called_once_with(
            artifact="examplegame",
            platforms=["win64game"],
            variant="release",
            remote_dir=elipy2.filer_paths.get_code_build_root_path("main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=[],
        )

    @patch("elipy2.local_paths.get_platform_path")
    def test_fetch_code_fb2019_no_bilbo_fbenv_args(self, mock_path):
        self.mock_fb_version.return_value = True
        self.mock_use_bilbo.return_value = False
        mock_path.return_value = "path"
        self.filer.fetch_code(
            "main",
            "1234",
            "win64game",
            "release",
            use_fbenv_copy=True,
            fbenv_copy_args=["/XF *.pdb"],
        )
        assert self.mock_robocopy.call_count == 0
        self.mock_pullfrostbitebuild.assert_called_once_with(
            artifact=fbenv_layer.get_exe_name(),
            platforms=["win64game"],
            variant="release",
            remote_dir=elipy2.filer_paths.get_code_build_root_path("main", "1234"),
            mirror=True,
            label="1234",
            copy_build_args=["/XF *.pdb"],
        )

    @patch("elipy2.local_paths.get_platform_path")
    def test_fetch_code_fb2019_no_bilbo_fetch_tests(self, mock_path):
        self.mock_fb_version.return_value = True
        self.mock_use_bilbo.return_value = False
        mock_path.return_value = "path"
        self.filer.fetch_code("main", "1234", "win64game", "final", fetch_tests=True)
        assert self.mock_robocopy.call_count == 0
        self.mock_pullfrostbitebuild.assert_has_calls(
            [
                call(
                    artifact=fbenv_layer.get_exe_name(),
                    platforms=["win64game"],
                    variant="final",
                    remote_dir=elipy2.filer_paths.get_code_build_root_path("main", "1234"),
                    mirror=True,
                    label="1234",
                    copy_build_args=[],
                ),
                call(
                    artifact="tests",
                    platforms=["win64game"],
                    variant="final",
                    remote_dir=elipy2.filer_paths.get_code_build_root_path("main", "1234"),
                    label="1234",
                    copy_build_args=[],
                ),
            ]
        )
        assert self.mock_pullfrostbitebuild.call_count == 2

    @patch("elipy2.local_paths.get_platform_path")
    def test_fetch_code_tool(self, mock_path):
        self.mock_fb_version.return_value = False
        mock_path.return_value = "path"
        self.filer.fetch_code("main", "1234", "tool", "release")
        calls = [
            call(
                os.path.join("test_buildshare", "Code", "main", "1234", "path"),
                os.path.join(os.environ["TNT_ROOT"], "Local", "Bin", "path"),
                purge=True,
                extra_args=["/XF", "*.pdb"],
            ),
            call(
                os.path.join("test_buildshare", "Code", "main", "1234", "path"),
                os.path.join(os.environ["TNT_ROOT"], "Local", "Bin", "path"),
                purge=True,
                extra_args=["/XF", "*.pdb"],
            ),
            call(
                os.path.join("test_buildshare", "Code", "main", "1234", "path"),
                os.path.join(os.environ["TNT_ROOT"], "Local", "Bin", "path"),
                purge=True,
                extra_args=["/XF", "*.pdb"],
            ),
        ]

        self.mock_robocopy.assert_has_calls(calls, any_order=True)

    @patch("elipy2.local_paths.get_platform_path")
    def test_fetch_code_custom_destination(self, mock_path):
        self.mock_fb_version.return_value = False
        mock_path.return_value = "path"
        self.filer.fetch_code("main", "1234", "win64game", "release", dest="/some/path")
        self.mock_robocopy.assert_called_once_with(
            os.path.join("test_buildshare", "Code", "main", "1234", "path"),
            "/some/path",
            extra_args=[],
            purge=True,
        )

    def test_fetch_code_with_unknown_platform(self):
        self.mock_fb_version.return_value = False
        with pytest.raises(ELIPYException):
            self.filer.fetch_code("main", "1234", "nintendo", "release")

    @patch("elipy2.core.use_bilbo")
    def test_fetch_code_not_use_bilbo(self, mock_use_bilbo):
        self.filer.fetch_code("main", "1234", "xb1", "release", use_bilbo=False)
        self.mock_use_bilbo.assert_not_called()

    @patch("elipy2.filer_paths.get_frosty_build_path")
    @patch("elipy2.local_paths.get_local_frosty_path")
    def test_fetch_frosty_build(self, mock_local_path, mock_filer_path):
        mock_local_path.return_value = "path"
        mock_filer_path.return_value = "filer_path"
        self.filer.fetch_frosty_build(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
            package_type="files",
            config="final",
            dest=None,
        )

        self.mock_robocopy.assert_called_once_with(os.path.join("filer_path"), os.path.join("path"))

    @patch("elipy2.filer_paths.get_frosty_build_path")
    @patch("elipy2.local_paths.get_local_frosty_path")
    def test_fetch_frosty_build_dest(self, mock_local_path, mock_filer_path):
        mock_local_path.return_value = "path"
        mock_filer_path.return_value = "filer_path"
        self.filer.fetch_frosty_build(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
            package_type="files",
            config="final",
            dest="dest",
        )

        self.mock_robocopy.assert_called_once_with(os.path.join("filer_path"), os.path.join("dest"))

    @patch("elipy2.filer_paths.get_frosty_build_path")
    @patch("elipy2.local_paths.get_local_frosty_path")
    def test_fetch_frosty_build_by_source(self, mock_local_path, mock_filer_path):
        filer_path = "\\\\custom\\filer\\path"
        local_path = "path"
        mock_local_path.return_value = local_path
        mock_filer_path.return_value = filer_path
        self.filer.fetch_frosty_build_by_source(
            source=filer_path,
            dest="",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(filer_path), os.path.join(local_path)
        )

    @patch("elipy2.filer_paths.get_frosty_build_path")
    @patch("elipy2.local_paths.get_local_frosty_path")
    def test_fetch_frosty_build_by_source_dest(self, mock_local_path, mock_filer_path):
        filer_path = "\\\\custom\\filer\\path"
        dest = "dest"
        mock_local_path.return_value = "path"
        mock_filer_path.return_value = filer_path
        self.filer.fetch_frosty_build_by_source(
            source=filer_path,
            dest=dest,
        )

        self.mock_robocopy.assert_called_once_with(os.path.join(filer_path), os.path.join(dest))

    @patch("elipy2.local_paths.get_local_frosty_path")
    @patch("os.path.join", side_effect=os.path.join)
    @patch("os.getcwd", return_value="d:\\dev\\elipy")
    def test_create_combine_stream_info_file(self, mock_getcwd, mock_join, mock_local_path):
        local_path = "path"
        mock_local_path.return_value = local_path
        self.filer.create_combine_stream_info_file(
            combine_data_branch="some-combine-data",
            combine_data_changelist="5678",
        )

        # Get the actual call arguments
        actual_args, actual_kwargs = self.mock_robocopy.call_args

        # Normalize both case and path separators for the first two positional arguments (paths)
        import os

        expected_args = (
            os.path.normcase(os.path.normpath("d:\\dev\\elipy")),
            os.path.normcase(os.path.normpath("path")),
        )
        actual_args_norm = (
            os.path.normcase(os.path.normpath(actual_args[0])),
            os.path.normcase(os.path.normpath(actual_args[1])),
        )
        assert actual_args_norm == expected_args
        assert actual_kwargs == {
            "single_file": True,
            "extra_args": ["some-combine-data_CL_5678.txt"],
        }

    @patch("elipy2.code.os.path.exists")
    def test_create_combine_stream_info_file_deployed(self, mock_exists):
        mock_exists.return_value = True
        with pytest.raises(ELIPYException):
            self.filer.create_combine_stream_info_file(
                combine_data_branch="some-combine-data",
                combine_data_changelist="5678",
            )

    def test_deploy_frosty_build(self):
        self.filer.deploy_frosty_build(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
            package_type="files",
            config="final",
            additional_configs=["performance", "retail"],
            dataset="wsdata",
            source=None,
        )
        copy_dest = os.path.join(
            "test_buildshare",
            "frosty",
            "projectName",
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "files",
            "WW",
            "final",
        )
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_frosty_path(),
            copy_dest,
            include_empty_dirs=False,
        )
        self.mock_bilbo_register_frosty.assert_called_once_with(
            copy_dest,
            data_changelist="1234",
            data_branch="some-data",
            code_changelist="4321",
            code_branch="some-code",
            platform="win64",
            package_type="files",
            region="WW",
            config="final",
            additional_configs=["performance", "retail"],
            dataset="wsdata",
            combine_data_changelist=None,
            combine_data_branch=None,
            combine_code_changelist=None,
            combine_code_branch=None,
        )

    def test_deploy_frosty_build_no_bilbo(self):
        self.mock_use_bilbo.return_value = False
        self.filer.deploy_frosty_build(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
            package_type="files",
            config="final",
            dataset="wsdata",
            source=None,
        )
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_frosty_path(),
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "files",
                "WW",
                "final",
            ),
            include_empty_dirs=False,
        )
        assert self.mock_bilbo_register_frosty.call_count == 0

    def test_deploy_frosty_build_custom_source(self):
        self.filer.deploy_frosty_build(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
            package_type="files",
            config="final",
            dataset="wsdata",
            source="/some/path",
        )
        self.mock_robocopy.assert_called_once_with(
            "/some/path",
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "files",
                "WW",
                "final",
            ),
            include_empty_dirs=False,
        )

    def test_deploy_frosty_build_no_source_specified(self):
        self.filer.deploy_frosty_build(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
            package_type="files",
            config="final",
            dataset="wsdata",
            source=None,
        )
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_frosty_path(),
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "files",
                "WW",
                "final",
            ),
            include_empty_dirs=False,
        )

    @patch("os.path.exists")
    def test_deploy_frosty_build_dest_exists_exception(self, mock_path_exists):
        mock_path_exists.return_value = True
        with pytest.raises(ELIPYException):
            self.filer.deploy_frosty_build(
                data_branch="some-data",
                data_changelist="1234",
                code_branch="some-code",
                code_changelist="4321",
                platform="win64",
                package_type="files",
                config="final",
                dataset="wsdata",
                source=None,
            )

    def test_deploy_frosty_build_combined(self):
        self.filer.deploy_frosty_build(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
            package_type="files",
            config="final",
            additional_configs=["performance", "retail"],
            dataset="wsdata",
            source=None,
            combine_data_branch="some-combine-data",
            combine_data_changelist="5678",
            combine_code_branch="some-combine-code",
            combine_code_changelist="8765",
        )
        copy_dest = os.path.join(
            "test_buildshare",
            "frosty",
            "projectName",
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "files",
            "WW",
            "final",
            "some-combine-data",
            "5678",
            "some-combine-code",
            "8765",
        )
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_frosty_path(),
            copy_dest,
            include_empty_dirs=False,
        )
        self.mock_bilbo_register_frosty.assert_called_once_with(
            copy_dest,
            data_changelist="1234",
            data_branch="some-data",
            code_changelist="4321",
            code_branch="some-code",
            platform="win64",
            package_type="files",
            region="WW",
            config="final",
            additional_configs=["performance", "retail"],
            dataset="wsdata",
            combine_data_changelist="5678",
            combine_data_branch="some-combine-data",
            combine_code_changelist="8765",
            combine_code_branch="some-combine-code",
        )

    def test_deploy_frosty_build_with_combine_params(self):
        self.filer.deploy_frosty_build(
            data_branch="some-data",
            data_changelist="1234",
            code_branch="some-code",
            code_changelist="4321",
            platform="win64",
            package_type="files",
            config="final",
            additional_configs=["performance", "retail"],
            dataset="wsdata",
            source=None,
            combine_data_branch="combined-data",
            combine_data_changelist="5678",
            combine_code_branch="combined-code",
            combine_code_changelist="8765",
            content_layer="Layer1",
        )
        copy_dest = os.path.join(
            "test_buildshare",
            "frosty",
            "projectName",
            "some-data",
            "1234",
            "some-code",
            "4321",
            "win64",
            "ContentLayer_Layer1",
            "files",
            "WW",
            "final",
            "combined-data",
            "5678",
            "combined-code",
            "8765",
        )
        self.mock_robocopy.assert_called_once_with(
            elipy2.local_paths.get_local_frosty_path(),
            copy_dest,
            include_empty_dirs=False,
        )
        self.mock_bilbo_register_frosty.assert_called_once_with(
            copy_dest,
            data_changelist="1234",
            data_branch="some-data",
            code_changelist="4321",
            code_branch="some-code",
            platform="win64",
            package_type="files",
            region="WW",
            config="final",
            additional_configs=["performance", "retail"],
            dataset="wsdata",
            combine_data_changelist="5678",
            combine_data_branch="combined-data",
            combine_code_changelist="8765",
            combine_code_branch="combined-code",
        )

    def test_deploy_delta_bundles(self):
        self.filer.deploy_delta_bundles(
            source="fakesource",
            data_branch="some-data",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
        )

        self.mock_robocopy.assert_called_once_with(
            "fakesource",
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "bundles",
                "delta",
            ),
        )

    def test_deploy_delta_bundles_non_default_dir_name(self):
        self.filer.deploy_delta_bundles(
            source="fakesource",
            data_branch="some-data",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
            bundles_dir_name="alt_bundles",
        )

        self.mock_robocopy.assert_called_once_with(
            "fakesource",
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "alt_bundles",
                "delta",
            ),
        )

    @patch("elipy2.code.os.path.exists")
    def test_deploy_delta_bundles_deployed(self, mock_exists):
        mock_exists.return_value = True
        with pytest.raises(ELIPYException):
            self.filer.deploy_delta_bundles(
                source="fakesource",
                data_branch="some-data",
                data_changelist=1234,
                code_branch="some-code",
                code_changelist=4321,
                platform="win64",
            )

    def test_deploy_head_bundles(self):
        self.filer.deploy_head_bundles(
            source="fakesource",
            data_branch="some-data",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
        )

        self.mock_robocopy.assert_called_once_with(
            "fakesource",
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "bundles",
                "head",
            ),
        )

    def test_deploy_head_bundles_non_default_dir_name(self):
        self.filer.deploy_head_bundles(
            source="fakesource",
            data_branch="some-data",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
            bundles_dir_name="alt_bundles",
        )

        self.mock_robocopy.assert_called_once_with(
            "fakesource",
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "alt_bundles",
                "head",
            ),
        )

    @patch("elipy2.code.os.path.exists")
    def test_deploy_head_bundles_deployed(self, mock_exists):
        mock_exists.return_value = True
        with pytest.raises(ELIPYException):
            self.filer.deploy_head_bundles(
                source="fakesource",
                data_branch="some-data",
                data_changelist=1234,
                code_branch="some-code",
                code_changelist=4321,
                platform="win64",
            )

    def test_deploy_avalanche_combine_output(self):
        self.filer.deploy_avalanche_combine_output(
            source="fakesource",
            data_branch="some-data",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
        )

        self.mock_robocopy.assert_called_once_with(
            "fakesource",
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "combine_output",
            ),
        )

    def test_deploy_avalanche_combine_output_non_default_dir_name(self):
        self.filer.deploy_avalanche_combine_output(
            source="fakesource",
            data_branch="some-data",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
            output_dir_name="alt_combine_output",
        )

        self.mock_robocopy.assert_called_once_with(
            "fakesource",
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "alt_combine_output",
            ),
        )

    @patch("elipy2.filer.LOGGER.info")
    @patch("elipy2.code.os.path.exists")
    def test_deploy_avalanche_combine_output_deployed(self, mock_exists, mock_logger):
        mock_exists.return_value = True
        self.filer.deploy_avalanche_combine_output(
            source="fakesource",
            data_branch="some-datadata",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
        )
        output_path = os.path.join(
            "test_buildshare",
            "frosty",
            "projectName",
            "some-datadata",
            "1234",
            "some-code",
            "4321",
            "win64",
            "combine_output",
        )
        mock_logger.assert_called_once_with(
            "Cannot deploy avalanche combine output to {0}, path already exists!".format(
                output_path
            )
        )

    def test_deploy_state(self):
        self.filer.deploy_state(
            source="fakesource",
            data_branch="some-data",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
        )
        self.mock_robocopy.assert_called_once_with(
            "fakesource",
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "bundles",
                "state",
            ),
        )

    def test_deploy_state_non_default_bundle_dir_name(self):
        self.filer.deploy_state(
            source="fakesource",
            data_branch="some-data",
            data_changelist=1234,
            code_branch="some-code",
            code_changelist=4321,
            platform="win64",
            bundles_dir_name="alt_bundles",
        )
        self.mock_robocopy.assert_called_once_with(
            "fakesource",
            os.path.join(
                "test_buildshare",
                "frosty",
                "projectName",
                "some-data",
                "1234",
                "some-code",
                "4321",
                "win64",
                "alt_bundles",
                "state",
            ),
        )

    @patch("elipy2.code.os.path.exists")
    def test_deploy_state_deployed(self, mock_exists):
        mock_exists.return_value = True
        with pytest.raises(ELIPYException):
            self.filer.deploy_state(
                source="fakesource",
                data_branch="some-data",
                data_changelist=1234,
                code_branch="some-code",
                code_changelist=4321,
                platform="win64",
            )

    def test_fetch_baseline_state(self):
        self.filer.fetch_baseline_state(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            dest="some-dest",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "bundles",
                "state",
            ),
            "some-dest",
            purge=True,
        )

    def test_fetch_baseline_state_non_default_dir_name(self):
        self.filer.fetch_baseline_state(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            dest="some-dest",
            bundles_dir_name="alt_bundles",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "alt_bundles",
                "state",
            ),
            "some-dest",
            purge=True,
        )

    def test_fetch_delta_bundles(self):
        self.filer.fetch_delta_bundles(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            dest="some-dest",
        )
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "frosty",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "bundles",
                "delta",
            ),
            "some-dest",
            purge=True,
        )

    def test_fetch_delta_bundles_no_dest(self):
        with pytest.raises(ELIPYException):
            self.filer.fetch_delta_bundles(
                code_branch="some-code-branch",
                data_branch="some-data-branch",
                code_changelist="123",
                data_changelist="321",
                platform="ps4",
            )

    def test_fetch_head_bundles(self):
        self.filer.fetch_head_bundles(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            dest="some-dest",
        )
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "frosty",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "bundles",
                "head",
            ),
            "some-dest",
            purge=True,
        )

    def test_fetch_head_bundles_non_default_dir_name(self):
        self.filer.fetch_head_bundles(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            bundles_dir_name="alt_bundles",
            dest="some-dest",
        )
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "frosty",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "alt_bundles",
                "head",
            ),
            "some-dest",
            purge=True,
        )

    def test_fetch_baseline_state_no_dest(self):
        self.filer.fetch_baseline_state(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "bundles",
                "state",
            ),
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "local", "baseline_state", "ps4"),
            purge=True,
        )

    def test_fetch_baseline_bundles(self):
        self.filer.fetch_baseline_bundles(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            dest="some-dest",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "bundles",
                "delta",
            ),
            "some-dest",
            purge=True,
        )

    def test_fetch_baseline_bundles_dest(self):
        self.filer.fetch_baseline_bundles(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "bundles",
                "delta",
            ),
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "local", "bundles", "base"),
            purge=True,
        )

    def test_fetch_baseline_bundles_non_default_dir_name(self):
        self.filer.fetch_baseline_bundles(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            dest="some-dest",
            bundles_dir_name="alt_bundles",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "alt_bundles",
                "delta",
            ),
            "some-dest",
            purge=True,
        )

    def test_fetch_baseline_bundles_dest_non_default_dir_name(self):
        self.filer.fetch_baseline_bundles(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            bundles_dir_name="alt_bundles",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "alt_bundles",
                "delta",
            ),
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "local", "alt_bundles", "base"),
            purge=True,
        )

    def test_fetch_baseline_bundles_head(self):
        self.filer.fetch_baseline_bundles_head(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            dest="some-dest",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "bundles",
                "head",
            ),
            "some-dest",
            purge=True,
        )

    def test_fetch_baseline_bundles_head_no_dest(self):
        self.filer.fetch_baseline_bundles_head(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "bundles",
                "head",
            ),
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "local", "bundles", "base"),
            purge=True,
        )

    def test_fetch_baseline_bundles_head_non_default_dir_name(self):
        self.filer.fetch_baseline_bundles_head(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            dest="some-dest",
            bundles_dir_name="alt_bundles",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "alt_bundles",
                "head",
            ),
            "some-dest",
            purge=True,
        )

    def test_fetch_baseline_bundles_head_no_dest_non_default_dir_name(self):
        self.filer.fetch_baseline_bundles_head(
            code_branch="some-code-branch",
            data_branch="some-data-branch",
            code_changelist="123",
            data_changelist="321",
            platform="ps4",
            bundles_dir_name="alt_bundles",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-data-branch",
                "321",
                "some-code-branch",
                "123",
                "ps4",
                "alt_bundles",
                "head",
            ),
            os.path.join(elipy2.frostbite_core.get_tnt_root(), "local", "alt_bundles", "base"),
            purge=True,
        )

    def test_fetch_baseline_win64_chunkmanifest(self):
        self.filer.fetch_baseline_win64_chunkmanifest(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch",
            config="retail",
            region="ww",
            destination="some-destination",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-branch",
                "123",
                "some-other-branch",
                "321",
                "win64",
                "patch",
                "ww",
                "retail",
            ),
            "some-destination",
            extra_args=["chunkmanifest.txt", "/MIR"],
        )

    def test_fetch_baseline_win64_chunkmanifest_failure_no_destination(self):
        with pytest.raises(ELIPYException):
            self.filer.fetch_baseline_win64_chunkmanifest(
                data_branch="some-branch",
                data_changelist=123,
                code_branch="some-other-branch",
                code_changelist=321,
                package_type="patch",
                config="retail",
                region="ww",
            )

    def test_fetch_baseline_win64_chunkmanifest_combine(self):
        self.filer.fetch_baseline_win64_chunkmanifest(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch",
            config="retail",
            region="ww",
            destination="some-destination",
            combine_data_branch="combine-branch",
            combine_data_changelist=456,
            combine_code_branch="some-other-combine-branch",
            combine_code_changelist=654,
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-branch",
                "123",
                "some-other-branch",
                "321",
                "win64",
                "patch",
                "ww",
                "retail",
                "combine-branch",
                "456",
                "some-other-combine-branch",
                "654",
            ),
            "some-destination",
            extra_args=["chunkmanifest.txt", "/MIR"],
        )

    def test_baseline_xb1_layout(self):
        self.filer.fetch_baseline_xb1_layout(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch",
            config="retail",
            region="ww",
            destination="some-destination",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-branch",
                "123",
                "some-other-branch",
                "321",
                "xb1",
                "patch",
                "ww",
                "retail",
            ),
            "some-destination",
            extra_args=["layout.xml", "/MIR"],
        )

    def test_baseline_xb1_layout_no_dest_exception(self):
        with pytest.raises(ELIPYException):
            self.filer.fetch_baseline_xb1_layout(
                data_branch="some-branch",
                data_changelist=123,
                code_branch="some-other-branch",
                code_changelist=321,
                package_type="patch",
                config="retail",
                region="ww",
            )

    @patch("elipy2.os.listdir")
    def test_fetch_baseline_xb_priorpackage_xb1_old(self, mock_listdir):
        self.mock_fb_version.return_value = False
        mock_listdir.return_value = [
            "q5ha1ztykcgvj",
            "q5ha1ztykcgvj.phd",
            "zwks512sysnyr",
            "zwks512sysnyr.phd",
            "chunkmanifest.txt",
            "layout*.xml",
        ]
        assert self.filer.fetch_baseline_xb_priorpackage(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch",
            config="retail",
            region="ww",
            destination="some-destination",
            platform="xb1",
        ) == os.path.join("some-destination", "q5ha1ztykcgvj")
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-branch",
                "123",
                "some-other-branch",
                "321",
                "xb1",
                "patch",
                "ww",
                "retail",
            ),
            "some-destination",
            purge=True,
            extra_args=[
                "*q5ha1ztykcgvj",
                "*q5ha1ztykcgvj.phd",
                "*zwks512sysnyr",
                "*zwks512sysnyr.phd",
                "chunkmanifest.txt",
                "layout*.xml",
            ],
        )

    @patch("elipy2.os.listdir")
    def test_fetch_baseline_xb_priorpackage_xb1_new(self, mock_listdir):
        self.mock_fb_version.return_value = True
        mock_listdir.return_value = [
            "q5ha1ztykcgvj_x.xvc",
            "q5ha1ztykcgvj_x.phd",
            "chunkmanifest.txt",
            "layout*.xml",
        ]
        assert self.filer.fetch_baseline_xb_priorpackage(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch",
            config="retail",
            region="ww",
            destination="some-destination",
            platform="xb1",
        ) == os.path.join("some-destination", "q5ha1ztykcgvj_x.xvc")
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-branch",
                "123",
                "some-other-branch",
                "321",
                "xb1",
                "patch",
                "ww",
                "retail",
            ),
            "some-destination",
            purge=True,
            extra_args=[
                "*q5ha1ztykcgvj_x.xvc",
                "*q5ha1ztykcgvj_x.phd",
                "chunkmanifest.txt",
                "layout*.xml",
            ],
        )

    @patch("elipy2.os.listdir")
    def test_fetch_baseline_xb_priorpackage_xbsx(self, mock_listdir):
        mock_listdir.return_value = [
            "q5ha1ztykcgvj_xs.xvc",
            "q5ha1ztykcgvj_xs.phd",
            "chunkmanifest.txt",
            "layout*.xml",
        ]
        assert self.filer.fetch_baseline_xb_priorpackage(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch",
            config="retail",
            region="ww",
            destination="some-destination",
            platform="xbsx",
        ) == os.path.join("some-destination", "q5ha1ztykcgvj_xs.xvc")
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-branch",
                "123",
                "some-other-branch",
                "321",
                "xbsx",
                "patch",
                "ww",
                "retail",
            ),
            "some-destination",
            purge=True,
            extra_args=[
                "*q5ha1ztykcgvj_xs.xvc",
                "*q5ha1ztykcgvj_xs.phd",
                "chunkmanifest.txt",
                "layout*.xml",
            ],
        )

    @patch("elipy2.os.listdir", MagicMock())
    def test_fetch_baseline_xb_priorpackage_xb1_no_dest_exception(self):
        with pytest.raises(ELIPYException):
            self.filer.fetch_baseline_xb_priorpackage(
                data_branch="some-branch",
                data_changelist="123",
                code_branch="some-other-branch",
                code_changelist="321",
                package_type="patch",
                config="retail",
                region="ww",
                destination=None,
                platform="xb1",
            )

    @patch("elipy2.os.listdir", MagicMock())
    def test_fetch_baseline_xb_priorpackage_wrong_platform(self):
        with pytest.raises(ELIPYException):
            self.filer.fetch_baseline_xb_priorpackage(
                data_branch="some-branch",
                data_changelist="123",
                code_branch="some-other-branch",
                code_changelist="321",
                package_type="patch",
                config="retail",
                region="ww",
                destination="some_destination",
                platform="ps5",
            )

    @patch("elipy2.os.listdir")
    def test_fetch_baseline_xb_priorpackage_xbsx_exception_file_not_found(self, mock_listdir):
        mock_listdir.return_value = [
            "correct_file_missing",
            "q5ha1ztykcgvj_xs.phd",
            "chunkmanifest.txt",
            "layout*.xml",
        ]
        with pytest.raises(ELIPYException):
            self.filer.fetch_baseline_xb_priorpackage(
                data_branch="some-branch",
                data_changelist="123",
                code_branch="some-other-branch",
                code_changelist="321",
                package_type="patch",
                config="retail",
                region="ww",
                destination="some-destination",
                platform="xbsx",
            )

    @patch("elipy2.os.listdir")
    def test_fetch_baseline_xb_priorpackage_xbsx_combine(self, mock_listdir):
        mock_listdir.return_value = [
            "q5ha1ztykcgvj_xs.xvc",
            "q5ha1ztykcgvj_xs.phd",
            "chunkmanifest.txt",
            "layout*.xml",
        ]
        assert self.filer.fetch_baseline_xb_priorpackage(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch",
            config="retail",
            region="ww",
            destination="some-destination",
            platform="xbsx",
            combine_data_branch="combine-branch",
            combine_data_changelist="456",
            combine_code_branch="other-combine-branch",
            combine_code_changelist="654",
        ) == os.path.join("some-destination", "q5ha1ztykcgvj_xs.xvc")
        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-branch",
                "123",
                "some-other-branch",
                "321",
                "xbsx",
                "patch",
                "ww",
                "retail",
                "combine-branch",
                "456",
                "other-combine-branch",
                "654",
            ),
            "some-destination",
            purge=True,
            extra_args=[
                "*q5ha1ztykcgvj_xs.xvc",
                "*q5ha1ztykcgvj_xs.phd",
                "chunkmanifest.txt",
                "layout*.xml",
            ],
        )

    def test_baseline_ps4_package(self):
        self.filer.fetch_baseline_ps4_package(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch",
            config="retail",
            region="ww",
            destination="some-destination",
        )

    def test_baseline_ps_package(self):
        self.filer.fetch_baseline_ps_package(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch",
            config="retail",
            region="ww",
            destination="some-destination",
            platform="ps4",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-branch",
                "123",
                "some-other-branch",
                "321",
                "ps4",
                "patch",
                "ww",
                "retail",
            ),
            "some-destination",
            extra_args=["/XF", "*.iso", "/MIR"],
        )

    def test_baseline_ps5_package(self):
        self.filer.fetch_baseline_ps_package(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch",
            config="retail",
            region="ww",
            destination="some-destination",
            platform="ps5",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-branch",
                "123",
                "some-other-branch",
                "321",
                "ps5",
                "patch",
                "ww",
                "retail",
            ),
            "some-destination",
            extra_args=["/XF", "*.iso", "/MIR"],
        )

    def test_baseline_ps5_package_combine(self):
        self.filer.fetch_baseline_ps_package(
            data_branch="some-branch",
            data_changelist="123",
            code_branch="some-other-branch",
            code_changelist="321",
            package_type="patch_combine",
            config="retail",
            region="ww",
            destination="some-destination",
            platform="ps5",
            combine_data_branch="combine-branch",
            combine_data_changelist="456",
            combine_code_branch="other-combine-branch",
            combine_code_changelist="654",
        )

        self.mock_robocopy.assert_called_once_with(
            os.path.join(
                "test_buildshare",
                "baselines",
                elipy2.frostbite_core.get_licensee_id(),
                "some-branch",
                "123",
                "some-other-branch",
                "321",
                "ps5",
                "patch_combine",
                "ww",
                "retail",
                "combine-branch",
                "456",
                "other-combine-branch",
                "654",
            ),
            "some-destination",
            extra_args=["/XF", "*.iso", "/MIR"],
        )

    @patch("elipy2.build_metadata.BuildMetadataManager.delete_build")
    @patch("elipy2.filer.FilerUtils.get_case_sensitive_path")
    @patch("requests.request")
    @patch("time.time")
    @patch("elipy2.secrets.get_secrets")
    @patch("elipy2.LOGGER.info")
    def test_onefs_api_delete_on_success(
        self,
        mock_logger,
        mock_get_secrets,
        mock_time,
        mock_requests,
        mock_get_case_sensitive_path,
        mock_bilbo_delete,
    ):
        mock_bilbo_delete.return_value = ""
        mock_get_secrets.return_value = {
            "test": {"domain": "blargh", "username": "blargh1", "password": "blargh2"}
        }
        mock_requests.return_value = type(
            "res", (object,), {"status_code": 200, "text": "uwutm8", "ok": True}
        )()
        mock_time.return_value = 5
        path = "\\\\filer.dice.ad.ea.com\\builds\\project\\local\\123456"
        mock_get_case_sensitive_path.return_value = path

        filer.FilerUtils.delete_with_onefs_api(path=path)
        mock_logger.assert_called_with(
            "Successfully deleted {} after {} seconds with {}".format(
                path, 0, "code 200 : [uwutm8]"
            )
        )

    @patch("elipy2.filer.FilerUtils.get_case_sensitive_path")
    @patch("elipy2.secrets.get_secrets")
    def test_onefs_api_delete_key_error(self, mock_get_secrets, mock_get_case_sensitive_path):
        mock_get_secrets.side_effect = KeyError()
        path = "\\\\filer.dice.ad.ea.com\\builds\\project\\local\\123456"
        mock_get_case_sensitive_path.return_value = path
        with pytest.raises(ELIPYException):
            filer.FilerUtils.delete_with_onefs_api(path=path)

    @patch("elipy2.filer.FilerUtils.get_case_sensitive_path")
    @patch("requests.request")
    @patch("time.time", MagicMock())
    @patch("elipy2.secrets.get_secrets")
    def test_onefs_api_delete_failed_validation(
        self, mock_get_secrets, mock_requests, mock_get_case_sensitive_path
    ):
        with pytest.raises(ELIPYException):
            mock_get_secrets.return_value = {
                "test": {"domain": "blargh", "username": "blargh1", "password": "blargh2"}
            }
            mock_requests.return_value = type(
                "res", (object,), {"status_code": 500, "text": ":(", "ok": False}
            )()
            path = "\\\\filer.dice.ad.ea.com\\builds\\project\\local\\123456"
            mock_get_case_sensitive_path.return_value = path
            filer.FilerUtils.delete_with_onefs_api(path=path)

    @patch("elipy2.filer.FilerUtils.get_case_sensitive_path")
    @patch("requests.request")
    @patch("time.time", MagicMock())
    @patch("elipy2.secrets.get_secrets")
    def test_onefs_api_delete_failed_deletion(
        self, mock_get_secrets, mock_requests, mock_get_case_sensitive_path
    ):
        with pytest.raises(ELIPYException):
            mock_get_secrets.return_value = {
                "test": {"domain": "blargh", "username": "blargh1", "password": "blargh2"}
            }
            mock_requests.side_effect = [
                type("res", (object,), {"status_code": 500, "text": ":(", "ok": True})(),
                type("res", (object,), {"status_code": 500, "text": ":(", "ok": False})(),
            ]
            path = "\\\\filer.dice.ad.ea.com\\builds\\project\\local\\123456"
            mock_get_case_sensitive_path.return_value = path
            filer.FilerUtils.delete_with_onefs_api(path=path)

    @patch("elipy2.build_metadata.BuildMetadataManager.delete_build")
    @patch("os.path.exists")
    @patch("elipy2.filer.FilerUtils.get_case_sensitive_path")
    def test_onefs_api_delete_path_doesnt_exist(
        self, mock_get_case_sensitive_path, mock_os_path_exists, mock_bilbo_delete
    ):
        mock_get_case_sensitive_path.side_effect = ELIPYException()
        mock_os_path_exists.return_value = False
        path = "\\\\filer.dice.ad.ea.com\\builds\\project\\local\\123456"
        mock_get_case_sensitive_path.return_value = path

        filer.FilerUtils.delete_with_onefs_api(path=path)
        mock_bilbo_delete.assert_called_once_with(path)

    @patch("elipy2.filer.FilerUtils._run_delete_with_onefs_api")
    @patch("os.path.exists")
    @patch("elipy2.filer.FilerUtils.get_case_sensitive_path")
    def test_onefs_api_delete_path_exist_but_is_broken(
        self, mock_get_case_sensitive_path, mock_os_path_exists, mock_delete_internal
    ):
        mock_get_case_sensitive_path.side_effect = ELIPYException()
        mock_os_path_exists.return_value = True
        path = "\\\\filer.dice.ad.ea.com\\builds\\project\\local\\123456"
        mock_get_case_sensitive_path.return_value = path
        with pytest.raises(ELIPYException):
            filer.FilerUtils.delete_with_onefs_api(path=path)
            assert mock_delete_internal.call_count == 0

    @patch("base64.b64encode")
    @patch("elipy2.filer.FilerUtils.get_case_sensitive_path")
    @patch("requests.request")
    @patch("elipy2.secrets.get_secrets")
    def test_onefs_api_delete_usrpass(
        self,
        mock_get_secrets,
        mock_requests,
        mock_get_case_sensitive_path,
        mock_base64,
    ):
        mock_get_secrets.return_value = {
            "test_1": {"domain": "domain_1", "username": "user_1", "password": "pass_1"},
            "test_2": {"domain": "domain_2", "username": "user_2", "password": "pass_2"},
        }
        mock_requests.return_value = type(
            "res", (object,), {"status_code": 200, "text": "uwutm8", "ok": True}
        )()
        path = "\\\\filer.dice.ad.ea.com\\builds\\project\\local\\123456"
        mock_get_case_sensitive_path.return_value = path
        filer.FilerUtils.delete_with_onefs_api(path=path)
        mock_base64.assert_called_once_with(f"domain_1\\user_1:pass_1".encode("utf-8"))

    @patch("elipy2.filer.FilerUtils.get_case_sensitive_path")
    @patch("requests.request")
    @patch("elipy2.secrets.get_secrets")
    def test_onefs_api_delete_usrpass_none_found(
        self,
        mock_get_secrets,
        mock_requests,
        mock_get_case_sensitive_path,
    ):
        mock_get_secrets.return_value = {}
        mock_requests.return_value = type(
            "res", (object,), {"status_code": 200, "text": "uwutm8", "ok": True}
        )()
        path = "\\\\filer.dice.ad.ea.com\\builds\\project\\local\\123456"
        mock_get_case_sensitive_path.return_value = path
        with pytest.raises(StopIteration):
            filer.FilerUtils.delete_with_onefs_api(path=path)

    @patch("os.listdir")
    @patch("os.path.join")
    def test_get_case_sensitive_path_success(self, mock_osjoin, mock_listdir):
        path = "\\\\filer.dice.ad.ea.com\\Builds\\Kingston"
        valid_path = "\\\\filer.dice.ad.ea.com\\Builds\\Kingston"
        mock_listdir.return_value = ["Kingston"]
        mock_osjoin.return_value = path
        test = filer.FilerUtils.get_case_sensitive_path(path=path)
        assert test == valid_path, got_but_expected(test, valid_path)

    @patch("os.listdir")
    def test_get_case_sensitive_path_failed(self, mock_listdir):
        with pytest.raises(ELIPYException):
            mock_listdir.return_value = ["\\Kingston"]
            path = "\\\\filer.dice.ad.ea.com\\Builds\\blargh"
            valid_path = "\\\\filer.dice.ad.ea.com\\Builds\\Kingston"
            test = filer.FilerUtils.get_case_sensitive_path(path=path)

    @patch("elipy2.core.run")
    def test_cancel_network_connection_all(self, mock_run):
        filer.FilerUtils.delete_network_connection()
        mock_run.assert_called_once_with(["net", "use", "*", "/delete", "/y"], print_std_out=True)

    @patch("elipy2.core.run")
    def test_cancel_network_connection_with_argument(self, mock_run):
        mapped_drive = "E:"
        network_path = "some_path"
        filer.FilerUtils.delete_network_connection(
            mapped_drive=mapped_drive, network_path=network_path
        )
        mock_run.assert_called_once_with(
            ["net", "use", mapped_drive, network_path, "/delete", "/y"], print_std_out=True
        )

    @patch("elipy2.core.run")
    def test_auth_network_connection_variable(self, mock_run):
        mapped_drive = "E:"
        network_path = "some_path"
        username = "user1"
        password = "password1"
        filer.FilerUtils.auth_network_connection(mapped_drive, network_path, username, password)
        mock_run.assert_called_once_with(
            ["net", "use", "E:", "some_path", "%FILER_PASSWORD%", "/USER:%FILER_USERNAME%"],
            print_std_out=True,
            env_patch={"FILER_USERNAME": "user1", "FILER_PASSWORD": "password1"},
        )

    def test_auth_network_connection_undefined_network(self):
        mapped_drive = "E:"
        username = "user1"
        password = "password1"
        with pytest.raises(ELIPYException):
            filer.FilerUtils.auth_network_connection(
                mapped_drive=mapped_drive, username=username, password=password
            )

    @patch("elipy2.secrets.get_secrets")
    @patch("elipy2.core.run")
    def test_auth_network_connection_vault(self, mock_run, mock_get_secrets):
        mapped_drive = "E:"
        network_path = "some_path"
        username = "user1"
        password = "password1"
        mock_get_secrets.return_value = {
            "test": {"domain": "filer", "filer_username": username, "filer_password": password}
        }
        filer.FilerUtils.auth_network_connection(mapped_drive, network_path)
        mock_run.assert_called_once_with(
            ["net", "use", "E:", "some_path", "%FILER_PASSWORD%", "/USER:%FILER_USERNAME%"],
            print_std_out=True,
            env_patch={"FILER_USERNAME": "user1", "FILER_PASSWORD": "password1"},
        )

    @patch("elipy2.secrets.get_secrets")
    @patch("elipy2.core.run")
    def test_auth_network_connection_no_cred(self, mock_run, mock_get_secrets):
        mapped_drive = "E:"
        network_path = "some_path"
        username = None
        password = None
        mock_get_secrets.return_value = {
            "test": {"domain": "filer", "filer_username": username, "filer_password": password}
        }
        filer.FilerUtils.auth_network_connection(mapped_drive, network_path)
        mock_run.assert_called_once_with(
            ["net", "use", "E:", "some_path"],
            print_std_out=True,
            env_patch={"FILER_USERNAME": None, "FILER_PASSWORD": None},
        )

    @patch("elipy2.secrets.get_secrets")
    @patch("elipy2.core.run")
    def test_auth_network_connection_cred_from_ess(self, mock_run, mock_get_secrets):
        mapped_drive = "E:"
        network_path = "some_path"
        mock_get_secrets.return_value = {
            "test_1": {
                "domain": "filer",
                "filer_username": "ess_user1",
                "filer_password": "ess_pass1",
            },
            "test_2": {
                "domain": "filer",
                "filer_username": "ess_user2",
                "filer_password": "ess_pass2",
            },
        }
        filer.FilerUtils.auth_network_connection(mapped_drive, network_path)
        mock_run.assert_called_once_with(
            ["net", "use", "E:", "some_path", "%FILER_PASSWORD%", "/USER:%FILER_USERNAME%"],
            print_std_out=True,
            env_patch={"FILER_USERNAME": "ess_user1", "FILER_PASSWORD": "ess_pass1"},
        )

    @patch("elipy2.secrets.get_secrets")
    @patch("elipy2.core.run")
    def test_auth_network_connection_no_creds_found_in_ess(self, mock_run, mock_get_secrets):
        mapped_drive = "E:"
        network_path = "some_path"
        mock_get_secrets.return_value = {}
        filer.FilerUtils.auth_network_connection(mapped_drive, network_path)
        mock_run.assert_called_once_with(
            ["net", "use", "E:", "some_path"],
            print_std_out=True,
            env_patch={"FILER_USERNAME": None, "FILER_PASSWORD": None},
        )

    @patch("elipy2.secrets.get_secrets")
    @patch("elipy2.core.run")
    def test_auth_network_connection_wrong_creds_keys_in_ess(self, mock_run, mock_get_secrets):
        mapped_drive = "E:"
        network_path = "some_path"
        mock_get_secrets.return_value = {
            "test_1": {
                "domain": "filer",
                "wrong_username": "ess_user1",
                "wrong_password": "ess_pass1",
            }
        }
        with pytest.raises(ELIPYException):
            filer.FilerUtils.auth_network_connection(mapped_drive, network_path)

    @patch("elipy2.secrets.get_secrets")
    @patch("elipy2.core.run")
    def test_auth_network_connection_bad_format_found_in_ess(self, mock_run, mock_get_secrets):
        mapped_drive = "E:"
        network_path = "some_path"
        mock_get_secrets.return_value = {"test_1": {"Not_a_value"}}
        with pytest.raises(TypeError):
            filer.FilerUtils.auth_network_connection(mapped_drive, network_path)

    @patch("elipy2.secrets.get_secrets")
    def test_auth_network_connection_exception(self, mock_get_secrets):
        mapped_drive = "E:"
        network_path = "some_path"
        mock_get_secrets.return_value = {
            "test": {"domain": "filer", "random_key": "user1", "random_password": "password1"}
        }
        with pytest.raises(ELIPYException):
            filer.FilerUtils.auth_network_connection(
                mapped_drive=mapped_drive, network_path=network_path
            )

    @patch("elipy2.core.run")
    def test_auth_network_connection_no_drive(self, mock_run):
        mapped_drive = None
        network_path = "some_path"
        username = "user1"
        password = "password1"
        filer.FilerUtils.auth_network_connection(mapped_drive, network_path, username, password)
        mock_run.assert_called_once_with(
            ["net", "use", "some_path", "%FILER_PASSWORD%", "/USER:%FILER_USERNAME%"],
            print_std_out=True,
            env_patch={"FILER_USERNAME": "user1", "FILER_PASSWORD": "password1"},
        )
