# Separate Combined Bundles Jobs Feature (COBRA-7366)

## Overview

This feature implements separate Jenkins jobs for creating combined bundles, eliminating duplication when building multiple configs or formats for the same platform. Previously, combined bundles were created inline within each frosty/patchfrosty job, leading to redundant work when building multiple variants (e.g., final/retail or files/digital) for the same platform.

## Problem Statement

**Before**: When building multiple configs/formats for the same platform (e.g., win64 final/retail or files/digital), each frosty/patchfrosty job would create the same combined bundles independently. Only the first job to complete would copy its bundles to the network share, while subsequent jobs' bundles were discarded.

**After**: Combined bundles are created once per platform in a separate job that runs before any frosty/patchfrosty variants. All subsequent jobs use the pre-created bundles from the network share.

## Architecture

### Job Flow (Feature Enabled)

```
┌─────────────────────────────────────────────────────────────┐
│                    Scheduler Start                          │
│              (frosty_start.groovy /                         │
│               patchfrosty_start.groovy)                     │
└─────────────────────┬───────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────┐
│              Combined Bundles Jobs                          │
│                  (Run in Parallel)                         │
│                                                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │ combined_bundles│ │ combined_bundles│ │ combined_bundles││
│  │     .win64      │ │      .ps5       │ │     .xbsx       ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────┬───────────────────────────────────────┘
                      │ (All must succeed)
                      ▼
┌─────────────────────────────────────────────────────────────┐
│              Frosty/Patchfrosty Jobs                        │
│                  (Run in Parallel)                         │
│                                                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │ frosty.win64    │ │ frosty.win64    │ │ frosty.ps5      ││
│  │ .files.final    │ │ .digital.retail │ │ .files.final    ││
│  │                 │ │                 │ │                 ││
│  │ Uses pre-created│ │ Uses pre-created│ │ Uses pre-created││
│  │ combined bundles│ │ combined bundles│ │ combined bundles││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### Key Components

1. **LibFrosty.groovy**: Contains the new `combined_bundles_job()` function
2. **Schedulers**: Updated to create and run combined bundles jobs first
3. **Elipy Scripts**: Modified to support pre-created bundles
4. **Branch Settings**: Extended with new configuration options

## Configuration

### Feature Flag Options

Add to your branch settings `combine_bundles` configuration:

```groovy
combine_bundles: [
    // ... existing settings ...
    
    // Enable separate combined bundles jobs
    use_separate_combined_job: true,
    
    // Platforms that should use separate jobs
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    
    // Job labeling strategy
    combined_job_label_type: 'poolbuild',  // or 'dedicated'
]
```

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `use_separate_combined_job` | Boolean | `false` | Enable/disable the feature |
| `combined_job_platforms` | List[String] | `[]` | Platforms to create separate jobs for |
| `combined_job_label_type` | String | `'poolbuild'` | Job labeling: `'poolbuild'` or `'dedicated'` |

### Job Labels

- **Poolbuild**: `poolbuild && {platform}` (e.g., `poolbuild && win64`)
- **Dedicated**: `{branch_name} combine-bundles {platform}` (e.g., `CH1-content-dev combine-bundles win64`)

## Implementation Details

### New Jenkins Jobs

When enabled, creates jobs with naming pattern:
```
{branch_name}.combined_bundles.{platform}
```

Examples:
- `CH1-content-dev-first-patch.combined_bundles.win64`
- `CH1-content-dev-first-patch.combined_bundles.ps5`
- `CH1-content-dev-first-patch.combined_bundles.xbsx`

### Script Parameters

#### Combined Bundles Script (`combined_bundles.py`)
- Creates combined bundles for a platform
- Optionally creates delta bundles for patchfrosty streams
- Deploys artifacts to network share

#### Updated Frosty Script (`frosty.py`)
- New flag: `--use-precreated-combined-bundles`
- When enabled, fetches combined bundles from network share instead of creating locally

#### Updated Patch Frosty Script (`patch_frosty.py`)
- New flags: `--use-precreated-combined-bundles`, `--use-precreated-delta-bundles`
- When enabled, uses pre-created bundles and delta bundles from network share

### Scheduler Changes

#### Frosty Scheduler (`frosty_start.groovy`)
1. Checks if separate combined jobs are enabled
2. Creates and runs combined bundles jobs in parallel
3. Waits for all combined bundles jobs to complete successfully
4. Only then runs frosty jobs with pre-created bundles flag

#### Patchfrosty Scheduler (`patchfrosty_start.groovy`)
1. Similar to frosty scheduler
2. Additionally handles delta bundles creation
3. Passes baseline parameters for delta bundle creation

## Benefits

1. **Eliminates Duplication**: Combined bundles created once per platform instead of per variant
2. **Improved Build Times**: Reduces overall build time by avoiding redundant work
3. **Better Resource Utilization**: More efficient use of build machines
4. **Cleaner Separation**: Combined bundles creation isolated from game packaging
5. **Easier Debugging**: Separate logs and failure isolation for combined bundles

## Backward Compatibility

The feature is **fully backward compatible**:

- **Default Behavior**: Feature is disabled by default (`use_separate_combined_job: false`)
- **Existing Branches**: Continue to work unchanged without any configuration changes
- **Gradual Adoption**: Can be enabled per branch and per platform
- **Quick Rollback**: Can be disabled immediately by changing the flag

## Migration Guide

### Phase 1: Enable on Test Branch
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: true,
    combined_job_platforms: ['win64'],  // Start with one platform
]
```

### Phase 2: Expand to More Platforms
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
]
```

### Phase 3: Consider Dedicated Machines (Optional)
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: true,
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
    combined_job_label_type: 'dedicated',
]
```

## Troubleshooting

### Common Issues

1. **Job Not Found**: Ensure seed jobs have been regenerated after configuration change
2. **Label Mismatch**: Verify build machines have correct labels for chosen label type
3. **Parameter Errors**: Check that all required parameters are passed correctly
4. **Dependency Failures**: Combined bundles job failure will fail entire scheduler

### Quick Rollback

To immediately disable the feature:
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: false,  // Disable feature
]
```

### Monitoring

Monitor these aspects after enabling:
- Build success rates
- Total build times
- Resource utilization
- Network share usage
- Error patterns

## Files Modified

### Jenkins Configuration
- `src/com/ea/lib/jobs/LibFrosty.groovy` - New combined bundles job function
- `src/scripts/schedulers/frosty/frosty_start.groovy` - Scheduler integration
- `src/scripts/schedulers/frosty/patchfrosty_start.groovy` - Scheduler integration

### Elipy Scripts
- `dice_elipy_scripts/frosty.py` - Pre-created bundles support
- `dice_elipy_scripts/patch_frosty.py` - Pre-created bundles and delta bundles support
- `dice_elipy_scripts/combined_bundles.py` - Existing standalone script (no changes needed)

### Documentation
- Branch settings configuration examples
- Testing and validation guides
- Migration procedures

## Testing

Comprehensive testing includes:
- Backward compatibility validation
- Feature functionality testing
- Performance impact measurement
- Error handling verification
- Rollback procedure testing

See `testing_guide.md` for detailed testing procedures.

## Support

For questions or issues:
1. Check the troubleshooting section above
2. Review the configuration guide
3. Test rollback procedure if needed
4. Contact the build team for assistance

## Implementation Status

### ✅ Completed Components

1. **Jenkins Job Creation** (`LibFrosty.groovy`)
   - New `combined_bundles_job()` function
   - Support for poolbuild and dedicated machine labels
   - Proper parameter handling and timeout configuration

2. **Scheduler Integration**
   - Updated `frosty_start.groovy` with combined bundles job dependency
   - Updated `patchfrosty_start.groovy` with combined bundles job dependency
   - Proper failure handling and job ordering

3. **Elipy Script Updates**
   - Added `--use-precreated-combined-bundles` to `frosty.py`
   - Added `--use-precreated-combined-bundles` and `--use-precreated-delta-bundles` to `patch_frosty.py`
   - Proper bundle fetching logic for pre-created bundles

4. **Feature Flag System**
   - Branch settings configuration options
   - Backward compatibility with default disabled state
   - Per-platform configuration support

5. **Documentation and Testing**
   - Comprehensive configuration guide
   - Testing procedures and validation scripts
   - Migration guide and troubleshooting documentation

### 🔄 Next Steps for Deployment

1. **Code Review**: Review all changes with the team
2. **Testing**: Execute validation procedures on test branches
3. **Seed Job Updates**: Regenerate Jenkins seed jobs with new configurations
4. **Gradual Rollout**: Enable on development branches first
5. **Monitoring**: Monitor build performance and success rates
6. **Production Rollout**: Enable on production branches after validation

## Future Enhancements

Potential future improvements:
1. Automatic platform detection based on build matrix
2. Parallel combined bundles creation optimization
3. Enhanced monitoring and metrics
4. Integration with build analytics
5. Support for custom bundle naming patterns
6. Advanced caching strategies for combined bundles
