@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM ELIPY TEST RUNNER
REM ============================================================================
REM
REM This script runs pytest for elipy2 and elipy-scripts modules.
REM
REM USAGE:
REM   .\run_elipy_tests.bat                     - Run all tests in both modules
REM   .\run_elipy_tests.bat elipy2              - Run all tests in elipy2
REM   .\run_elipy_tests.bat elipy-scripts       - Run all tests in elipy-scripts
REM   .\run_elipy_tests.bat path\to\test.py     - Run a specific test file
REM   .\run_elipy_tests.bat path\to\test.py::TestClass::test_method
REM                                             - Run a specific test method
REM
REM EXAMPLES:
REM   .\run_elipy_tests.bat elipy2 elipy2/tests/bilbo_v2/test_bilbo_v2.py
REM   .\run_elipy_tests.bat .\elipy2\tests\bilbo_v2\test_bilbo_v2.py
REM   .\run_elipy_tests.bat .\elipy2\tests\bilbo_v2\test_bilbo_v2.py::TestGetAttributes::test_no_verified_data
REM
REM NOTES:
REM   - The script automatically detects the module based on the path
REM   - Sets PYTHONPATH to include elipy2 and elipy-scripts modules
REM   - Coverage checks are disabled (-o addopts="" flag)
REM   - Verbose output is enabled for specific test runs (-v flag)
REM
REM ============================================================================

echo Setting up elipy test environment...

REM Set Python path to include elipy2 and elipy-scripts modules
set PYTHONPATH=%CD%\pycharm\elipy2;%CD%\pycharm\elipy-scripts;%PYTHONPATH%

REM Set environment variables
set ELIPY_CONFIG=elipy_test.yml
set ELIPY_TEST_RUN=TRUE
set TNT_ROOT=tnt_root
set GAME_DATA_DIR=game_data_dir
set GAME_ROOT=game_root
set EXE_MAIN_NAME=exe_main_name
set nantlocation=nantpath
set LICENSEE_ID=projectName
set fb_branch_id=test_branch
set USERNAME=test_user

echo Environment variables set.
echo Python path: %PYTHONPATH%
echo.

if "%1"=="" (
    echo No test specified. Running all tests.
    echo.
    echo Running elipy2 tests...
    cd pycharm\elipy2
    python -m pytest -o addopts=""
    cd ..\..

    echo.
    echo Running elipy-scripts tests...
    cd pycharm\elipy-scripts
    python -m pytest -o addopts=""
    cd ..\..
) else (
    echo Running specified test: %*

    REM Check if the argument is a module name (elipy2 or elipy-scripts)
    if "%1"=="elipy2" (
        cd pycharm\elipy2
        if "%2"=="" (
            python -m pytest -v -o addopts=""
        ) else (
            python -m pytest %2 %3 %4 %5 %6 %7 %8 %9 -v -o addopts=""
        )
        cd ..\..
    ) else if "%1"=="elipy-scripts" (
        cd pycharm\elipy-scripts
        if "%2"=="" (
            python -m pytest -v -o addopts=""
        ) else (
            python -m pytest %2 %3 %4 %5 %6 %7 %8 %9 -v -o addopts=""
        )
        cd ..\..
    ) else (
        REM Check if the path starts with pycharm\elipy2 or pycharm\elipy-scripts
        echo %1 | findstr /i /c:"pycharm\elipy2" > nul
        if not errorlevel 1 (
            cd pycharm\elipy2
            set TEST_PATH=%1
            set TEST_PATH=!TEST_PATH:pycharm\elipy2\=!
            python -m pytest !TEST_PATH! %2 %3 %4 %5 %6 %7 %8 %9 -v -o addopts=""
            cd ..\..
        ) else (
            echo %1 | findstr /i /c:"pycharm\elipy-scripts" > nul
            if not errorlevel 1 (
                cd pycharm\elipy-scripts
                set TEST_PATH=%1
                set TEST_PATH=!TEST_PATH:pycharm\elipy-scripts\=!
                python -m pytest !TEST_PATH! %2 %3 %4 %5 %6 %7 %8 %9 -v -o addopts=""
                cd ..\..
            ) else (
                REM Check if the path starts with .\elipy2\ or .\elipy-scripts\
                echo %1 | findstr /i /c:".\elipy2\" > nul
                if not errorlevel 1 (
                    cd pycharm\elipy2
                    set TEST_PATH=%1
                    set TEST_PATH=!TEST_PATH:.\elipy2\=!
                    python -m pytest !TEST_PATH! %2 %3 %4 %5 %6 %7 %8 %9 -v -o addopts=""
                    cd ..\..
                ) else (
                    echo %1 | findstr /i /c:".\elipy-scripts\" > nul
                    if not errorlevel 1 (
                        cd pycharm\elipy-scripts
                        set TEST_PATH=%1
                        set TEST_PATH=!TEST_PATH:.\elipy-scripts\=!
                        python -m pytest !TEST_PATH! %2 %3 %4 %5 %6 %7 %8 %9 -v -o addopts=""
                        cd ..\..
                    ) else (
                        REM Default to running in elipy-scripts directory
                        cd pycharm\elipy-scripts
                        python -m pytest %* -v -o addopts=""
                        cd ..\..
                    )
                )
            )
        )
    )
)

echo.
echo Test run complete!
