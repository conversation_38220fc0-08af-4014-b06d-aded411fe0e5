# Branch Settings Configuration Guide for Separate Combined Bundles Jobs

## Overview
This guide explains how to configure branch settings to use the new separate combined bundles jobs feature. This feature allows combined bundles to be created in dedicated Jenkins jobs instead of inline within frosty/patchfrosty jobs, eliminating duplication when building multiple configs/formats for the same platform.

## Configuration Options

### Basic Configuration

To enable separate combined bundles jobs, add the following to your branch settings `combine_bundles` configuration:

```groovy
combine_bundles: [
    // ... existing settings ...
    
    // Enable separate combined bundles jobs
    use_separate_combined_job: true,
    
    // Platforms that should use separate jobs
    combined_job_platforms: ['win64', 'ps5', 'xbsx'],
]
```

### Feature Flag Options

#### `use_separate_combined_job` (Boolean)
- **Default**: `false`
- **Description**: Controls whether to create separate Jenkins jobs for combined bundles creation
- **Values**:
  - `false`: Use existing inline behavior (backward compatibility)
  - `true`: Create separate combined bundles jobs

#### `combined_job_platforms` (List of Strings)
- **Default**: `[]` (empty list)
- **Description**: List of platforms that should use separate combined bundles jobs
- **Example**: `['win64', 'ps5', 'xbsx']`
- **Note**: Only platforms listed here will get separate jobs. Other platforms will use inline creation.

#### `combined_job_label_type` (String)
- **Default**: `'poolbuild'`
- **Description**: Controls the Jenkins job labeling strategy
- **Values**:
  - `'poolbuild'`: Use poolbuild labels (e.g., `poolbuild win64`)
  - `'dedicated'`: Use dedicated machine labels (e.g., `CH1-content-dev combine-bundles win64`)

## Job Naming Convention

Separate combined bundles jobs follow this naming pattern:
```
{branch_name}.combined_bundles.{platform}
```

Examples:
- `CH1-content-dev-first-patch.combined_bundles.win64`
- `CH1-content-dev-first-patch.combined_bundles.ps5`
- `CH1-content-dev-first-patch.combined_bundles.xbsx`

## Job Labels

### Poolbuild Labels (Default)
When `combined_job_label_type: 'poolbuild'`:
- Job label: `{poolbuild_label} {platform}`
- Example: `poolbuild win64`
- Uses the same poolbuild infrastructure as other jobs

### Dedicated Machine Labels
When `combined_job_label_type: 'dedicated'`:
- Job label: `{stream_name} combine-bundles {platform}`
- Example: `CH1-content-dev-first-patch combine-bundles win64`
- Requires dedicated machines with this specific label

## Configuration Examples

### Example 1: Basic Poolbuild Configuration
```groovy
combine_bundles: [
    combine_asset                : 'CombinedShippingMPLevels',
    combine_reference_job        : 'CH1-SP-content-dev-first-patch.patchdata.start',
    is_target_branch            : true,
    source_branch_code          : 'CH1-SP-content-dev',
    source_branch_data          : 'CH1-SP-content-dev-first-patch',
    
    // Enable separate jobs for all platforms
    use_separate_combined_job   : true,
    combined_job_platforms      : ['win64', 'ps5', 'xbsx'],
    combined_job_label_type     : 'poolbuild',  // Default
]
```

### Example 2: Dedicated Machines
```groovy
combine_bundles: [
    combine_asset                : 'CombinedShippingLevels',
    combine_reference_job        : 'CH1-release.data.start',
    is_target_branch            : true,
    source_branch_code          : 'CH1-release',
    source_branch_data          : 'CH1-release',
    
    // Use dedicated machines for combined bundles
    use_separate_combined_job   : true,
    combined_job_platforms      : ['win64'],
    combined_job_label_type     : 'dedicated',
]
```

### Example 3: Mixed Approach (Some Platforms Only)
```groovy
combine_bundles: [
    combine_asset                : 'CombinedShippingLevels',
    combine_reference_job        : 'CH1-release.data.start',
    is_target_branch            : true,
    source_branch_code          : 'CH1-release',
    source_branch_data          : 'CH1-release',
    
    // Only win64 uses separate job, others use inline
    use_separate_combined_job   : true,
    combined_job_platforms      : ['win64'],
]
```

### Example 4: Backward Compatibility (Feature Disabled)
```groovy
combine_bundles: [
    combine_asset        : 'CombinedShippingLevels',
    combine_reference_job: 'CH1-release.data.start',
    is_target_branch     : true,
    source_branch_code   : 'CH1-release',
    source_branch_data   : 'CH1-release',
    
    // Feature disabled - uses existing inline behavior
    // use_separate_combined_job: false,  // This is the default
]
```

## Job Dependencies and Execution Order

When separate combined bundles jobs are enabled:

1. **Combined Bundles Jobs**: Run first, in parallel for all configured platforms
2. **Dependency Check**: If any combined bundles job fails, the entire scheduler fails
3. **Frosty/Patchfrosty Jobs**: Only run after all combined bundles jobs complete successfully
4. **Bundle Usage**: Frosty/patchfrosty jobs automatically use `--use-precreated-combined-bundles` flag

## Migration Strategy

### Phase 1: Test on Development Branches
1. Enable the feature on development/test branches first
2. Verify that builds work correctly with both feature enabled and disabled
3. Monitor build times and resource usage

### Phase 2: Gradual Production Rollout
1. Enable for one platform at a time using `combined_job_platforms`
2. Monitor for any issues before expanding to more platforms
3. Can be quickly disabled by setting `use_separate_combined_job: false`

### Phase 3: Full Adoption
1. Enable for all platforms once confident in the implementation
2. Consider using dedicated machines for high-volume branches

## Troubleshooting

### Common Issues

1. **Job Not Found**: Ensure the combined bundles job is created in the seed jobs
2. **Label Mismatch**: Verify that machines have the correct labels for the chosen `combined_job_label_type`
3. **Parameter Mismatch**: Check that all required parameters are passed to the combined bundles job

### Rollback Procedure

To quickly disable the feature:
```groovy
combine_bundles: [
    // ... existing settings ...
    use_separate_combined_job: false,  // Disable the feature
]
```

This will immediately revert to the existing inline behavior without requiring any other changes.

## Benefits

1. **Eliminates Duplication**: Combined bundles created once per platform instead of per config/format
2. **Improved Build Times**: Reduces overall build time by avoiding redundant work
3. **Better Resource Utilization**: More efficient use of build machines
4. **Cleaner Build Logs**: Separate logs for combined bundles creation
5. **Easier Debugging**: Isolated failures in combined bundles creation

## Best Practices

1. **Start Small**: Begin with one platform and expand gradually
2. **Monitor Resources**: Watch build machine utilization and adjust as needed
3. **Use Poolbuild Initially**: Start with poolbuild labels before considering dedicated machines
4. **Test Thoroughly**: Verify both success and failure scenarios
5. **Document Changes**: Update team documentation when enabling the feature
