#!/usr/bin/env python3
"""
Validation script for separate combined bundles jobs feature (COBRA-7366).

This script validates the implementation by checking:
1. Job creation and configuration
2. Parameter passing
3. Dependency execution order
4. Artifact creation and deployment
5. Error handling

Usage:
    python validation_script.py --branch CH1-test-branch --jenkins-url https://jenkins.example.com
"""

import argparse
import json
import logging
import requests
import sys
import time
from typing import Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class JenkinsValidator:
    """Validates Jenkins job configuration and execution for combined bundles feature."""
    
    def __init__(self, jenkins_url: str, username: str, token: str):
        self.jenkins_url = jenkins_url.rstrip('/')
        self.auth = (username, token)
        self.session = requests.Session()
        self.session.auth = self.auth
    
    def get_job_config(self, job_name: str) -> Optional[Dict]:
        """Get Jenkins job configuration."""
        try:
            response = self.session.get(f"{self.jenkins_url}/job/{job_name}/api/json")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to get job config for {job_name}: {e}")
            return None
    
    def check_job_exists(self, job_name: str) -> bool:
        """Check if a Jenkins job exists."""
        config = self.get_job_config(job_name)
        return config is not None
    
    def get_build_info(self, job_name: str, build_number: int) -> Optional[Dict]:
        """Get build information."""
        try:
            response = self.session.get(
                f"{self.jenkins_url}/job/{job_name}/{build_number}/api/json"
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to get build info for {job_name}#{build_number}: {e}")
            return None
    
    def trigger_job(self, job_name: str, parameters: Dict[str, str] = None) -> Optional[int]:
        """Trigger a Jenkins job and return the build number."""
        try:
            if parameters:
                response = self.session.post(
                    f"{self.jenkins_url}/job/{job_name}/buildWithParameters",
                    data=parameters
                )
            else:
                response = self.session.post(f"{self.jenkins_url}/job/{job_name}/build")
            
            response.raise_for_status()
            
            # Wait for build to start and get build number
            time.sleep(2)
            job_info = self.get_job_config(job_name)
            if job_info and 'lastBuild' in job_info:
                return job_info['lastBuild']['number']
            return None
            
        except requests.RequestException as e:
            logger.error(f"Failed to trigger job {job_name}: {e}")
            return None


class CombinedBundlesValidator:
    """Main validator for combined bundles feature."""
    
    def __init__(self, jenkins_url: str, username: str, token: str, branch: str):
        self.jenkins = JenkinsValidator(jenkins_url, username, token)
        self.branch = branch
        self.results = []
    
    def log_result(self, test_name: str, passed: bool, message: str):
        """Log test result."""
        status = "PASS" if passed else "FAIL"
        logger.info(f"[{status}] {test_name}: {message}")
        self.results.append({
            'test': test_name,
            'passed': passed,
            'message': message
        })
    
    def validate_job_creation(self, platforms: List[str]) -> bool:
        """Validate that combined bundles jobs are created for specified platforms."""
        logger.info("Validating job creation...")
        all_passed = True
        
        for platform in platforms:
            job_name = f"{self.branch}.combined_bundles.{platform}"
            exists = self.jenkins.check_job_exists(job_name)
            
            if exists:
                self.log_result(
                    f"Job Creation - {platform}",
                    True,
                    f"Combined bundles job {job_name} exists"
                )
            else:
                self.log_result(
                    f"Job Creation - {platform}",
                    False,
                    f"Combined bundles job {job_name} does not exist"
                )
                all_passed = False
        
        return all_passed
    
    def validate_job_configuration(self, platform: str, expected_label_type: str) -> bool:
        """Validate job configuration including labels and parameters."""
        logger.info(f"Validating job configuration for {platform}...")
        
        job_name = f"{self.branch}.combined_bundles.{platform}"
        config = self.jenkins.get_job_config(job_name)
        
        if not config:
            self.log_result(
                f"Job Config - {platform}",
                False,
                f"Could not retrieve configuration for {job_name}"
            )
            return False
        
        # Check job label
        label = config.get('labelExpression', '')
        expected_patterns = {
            'poolbuild': f'poolbuild && {platform}',
            'dedicated': f'{self.branch} combine-bundles {platform}'
        }
        
        expected_label = expected_patterns.get(expected_label_type, '')
        label_correct = expected_label in label or label in expected_label
        
        self.log_result(
            f"Job Label - {platform}",
            label_correct,
            f"Label: {label}, Expected pattern: {expected_label}"
        )
        
        # Check parameters
        parameters = config.get('property', [])
        param_names = []
        for prop in parameters:
            if 'parameterDefinitions' in prop:
                for param in prop['parameterDefinitions']:
                    param_names.append(param.get('name', ''))
        
        required_params = [
            'code_changelist', 'data_changelist',
            'combine_code_changelist', 'combine_data_changelist'
        ]
        
        missing_params = [p for p in required_params if p not in param_names]
        params_correct = len(missing_params) == 0
        
        self.log_result(
            f"Job Parameters - {platform}",
            params_correct,
            f"Missing parameters: {missing_params}" if missing_params else "All required parameters present"
        )
        
        return label_correct and params_correct
    
    def validate_scheduler_integration(self) -> bool:
        """Validate that schedulers include combined bundles jobs."""
        logger.info("Validating scheduler integration...")
        
        # Check frosty scheduler
        frosty_job = f"{self.branch}.frosty.start"
        frosty_exists = self.jenkins.check_job_exists(frosty_job)
        
        self.log_result(
            "Frosty Scheduler",
            frosty_exists,
            f"Frosty scheduler {frosty_job} {'exists' if frosty_exists else 'missing'}"
        )
        
        # Check patchfrosty scheduler
        patchfrosty_job = f"{self.branch}.patchfrosty.start"
        patchfrosty_exists = self.jenkins.check_job_exists(patchfrosty_job)
        
        self.log_result(
            "Patchfrosty Scheduler",
            patchfrosty_exists,
            f"Patchfrosty scheduler {patchfrosty_job} {'exists' if patchfrosty_exists else 'missing'}"
        )
        
        return frosty_exists and patchfrosty_exists
    
    def validate_dependency_execution(self, scheduler_type: str = 'frosty') -> bool:
        """Validate that combined bundles jobs run before frosty/patchfrosty jobs."""
        logger.info(f"Validating dependency execution for {scheduler_type}...")
        
        scheduler_job = f"{self.branch}.{scheduler_type}.start"
        
        # Trigger scheduler
        build_number = self.jenkins.trigger_job(scheduler_job, {
            'code_changelist': '12345',
            'data_changelist': '12345'
        })
        
        if not build_number:
            self.log_result(
                f"Dependency Execution - {scheduler_type}",
                False,
                f"Failed to trigger {scheduler_job}"
            )
            return False
        
        # Wait for build to complete or timeout
        timeout = 300  # 5 minutes
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            build_info = self.jenkins.get_build_info(scheduler_job, build_number)
            if build_info and not build_info.get('building', True):
                break
            time.sleep(10)
        
        if not build_info:
            self.log_result(
                f"Dependency Execution - {scheduler_type}",
                False,
                f"Could not get build info for {scheduler_job}#{build_number}"
            )
            return False
        
        # Check if build was successful
        result = build_info.get('result', 'UNKNOWN')
        success = result == 'SUCCESS'
        
        self.log_result(
            f"Dependency Execution - {scheduler_type}",
            success,
            f"Build {scheduler_job}#{build_number} result: {result}"
        )
        
        return success
    
    def validate_backward_compatibility(self) -> bool:
        """Validate that disabling the feature maintains backward compatibility."""
        logger.info("Validating backward compatibility...")
        
        # This would require temporarily disabling the feature and testing
        # For now, we'll check that the old job patterns still work
        
        frosty_jobs = []
        platforms = ['win64', 'ps5', 'xbsx']
        configs = ['final']
        formats = ['combine']
        regions = ['ww']
        
        for platform in platforms:
            for config in configs:
                for format_type in formats:
                    for region in regions:
                        job_name = f"{self.branch}.frosty.Data.{platform}.{format_type}.{region}.{config}"
                        if self.jenkins.check_job_exists(job_name):
                            frosty_jobs.append(job_name)
        
        compatibility_ok = len(frosty_jobs) > 0
        
        self.log_result(
            "Backward Compatibility",
            compatibility_ok,
            f"Found {len(frosty_jobs)} existing frosty jobs"
        )
        
        return compatibility_ok
    
    def run_validation(self, platforms: List[str], label_type: str = 'poolbuild') -> bool:
        """Run complete validation suite."""
        logger.info(f"Starting validation for branch {self.branch}")
        logger.info(f"Platforms: {platforms}")
        logger.info(f"Label type: {label_type}")
        
        all_passed = True
        
        # Test 1: Job Creation
        if not self.validate_job_creation(platforms):
            all_passed = False
        
        # Test 2: Job Configuration
        for platform in platforms:
            if not self.validate_job_configuration(platform, label_type):
                all_passed = False
        
        # Test 3: Scheduler Integration
        if not self.validate_scheduler_integration():
            all_passed = False
        
        # Test 4: Dependency Execution (optional - requires triggering builds)
        # if not self.validate_dependency_execution('frosty'):
        #     all_passed = False
        
        # Test 5: Backward Compatibility
        if not self.validate_backward_compatibility():
            all_passed = False
        
        return all_passed
    
    def print_summary(self):
        """Print validation summary."""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r['passed'])
        failed_tests = total_tests - passed_tests
        
        logger.info("\n" + "="*60)
        logger.info("VALIDATION SUMMARY")
        logger.info("="*60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            logger.info("\nFAILED TESTS:")
            for result in self.results:
                if not result['passed']:
                    logger.info(f"  - {result['test']}: {result['message']}")


def main():
    parser = argparse.ArgumentParser(
        description='Validate separate combined bundles jobs implementation'
    )
    parser.add_argument('--branch', required=True, help='Branch name to validate')
    parser.add_argument('--jenkins-url', required=True, help='Jenkins URL')
    parser.add_argument('--username', required=True, help='Jenkins username')
    parser.add_argument('--token', required=True, help='Jenkins API token')
    parser.add_argument('--platforms', nargs='+', default=['win64', 'ps5', 'xbsx'],
                       help='Platforms to validate')
    parser.add_argument('--label-type', choices=['poolbuild', 'dedicated'],
                       default='poolbuild', help='Expected job label type')
    
    args = parser.parse_args()
    
    validator = CombinedBundlesValidator(
        args.jenkins_url, args.username, args.token, args.branch
    )
    
    success = validator.run_validation(args.platforms, args.label_type)
    validator.print_summary()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
