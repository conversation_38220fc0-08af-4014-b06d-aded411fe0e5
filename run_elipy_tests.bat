@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM ELIPY TEST RUNNER (V3)
REM ============================================================================
REM
REM This script runs tests for elipy2 and elipy-scripts modules.
REM
REM USAGE:
REM   .\run_elipy_tests_v3.bat                     - Run all tests in both modules
REM   .\run_elipy_tests_v3.bat elipy2              - Run all tests in elipy2
REM   .\run_elipy_tests_v3.bat elipy-scripts       - Run all tests in elipy-scripts
REM   .\run_elipy_tests_v3.bat path\to\test.py     - Run a specific test file
REM   .\run_elipy_tests_v3.bat path\to\test.py::TestClass::test_method
REM                                             - Run a specific test method
REM   .\run_elipy_tests_v3.bat pylint_elipy_scripts - Run pylint for elipy-scripts
REM
REM ============================================================================

if "%1"=="" (
    echo No test specified. Running all tests.
    echo.
    echo Running elipy2 tests...
    cd pycharm\elipy2
    python -m pytest
    cd ..\..

    echo.
    echo Running elipy-scripts tests...
    cd pycharm\elipy-scripts
    python -m pytest
    cd ..\..
) else (
    echo Running specified command: %*

    if "%1"=="elipy2" (
        cd pycharm\elipy2
        python setup.py test
        cd ..\..
    ) else if "%1"=="elipy-scripts" (
        cd pycharm\elipy-scripts
        python -m pytest
        cd ..\..
    ) else if "%1"=="pylint_elipy_scripts" (
        echo Running Pylint for dice_elipy_scripts...
        cd pycharm\elipy-scripts
        python -m pylint dice_elipy_scripts -r y --disable=unexpected-keyword-arg
        cd ..\..
    ) else (
        REM Check if the path starts with pycharm\elipy2 or pycharm\elipy-scripts
        echo %1 | findstr /i /c:"pycharm\elipy2" > nul
        if not errorlevel 1 (
            cd pycharm\elipy2
            set TEST_PATH=%1
            set TEST_PATH=!TEST_PATH:pycharm\elipy2\=!
            python -m pytest !TEST_PATH! %2 %3 %4 %5 %6 %7 %8 %9
            set PYTHONPATH=
            rmdir /s /q C:\Users\<USER>\vscode\temp_pytest_config
            cd ..\..
        ) else (
            echo %1 | findstr /i /c:"pycharm\elipy-scripts" > nul
            if not errorlevel 1 (
                cd pycharm\elipy-scripts
                set TEST_PATH=%1
                set TEST_PATH=!TEST_PATH:pycharm\elipy-scripts\=!
                python -m pytest !TEST_PATH! %2 %3 %4 %5 %6 %7 %8 %9
                set PYTHONPATH=
                rmdir /s /q C:\Users\<USER>\vscode\temp_pytest_config
                cd ..\..
            ) else (
                REM Check if the path starts with .\elipy2\ or .\elipy-scripts\
                echo %1 | findstr /i /c:".\elipy2\" > nul
                if not errorlevel 1 (
                    cd pycharm\elipy2
                    set TEST_PATH=%1
                    set TEST_PATH=!TEST_PATH:.\elipy2\=!                    python -m pytest !TEST_PATH! %2 %3 %4 %5 %6 %7 %8 %9                    set PYTHONPATH=                    rmdir /s /q C:\Users\<USER>\vscode\temp_pytest_config                    cd ..\..
                ) else (
                    echo %1 | findstr /i /c:".\elipy-scripts\" > nul
                    if not errorlevel 1 (
                        cd pycharm\elipy-scripts
                        set TEST_PATH=%1
                        set TEST_PATH=!TEST_PATH:.\elipy-scripts\=!
                        python -m pytest !TEST_PATH! %2 %3 %4 %5 %6 %7 %8 %9
                        set PYTHONPATH=
                        rmdir /s /q C:\Users\<USER>\vscode\temp_pytest_config
                        cd ..\..
                    ) else (
                        echo "Invalid argument. Please specify 'elipy2', 'elipy-scripts', 'pylint_elipy_scripts', or a valid test file path."
                    )
                )
            )
        )
    )
)

echo.
echo Test run complete!
