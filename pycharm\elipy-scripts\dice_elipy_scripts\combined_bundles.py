"""combined_bundles.py

<PERSON><PERSON> to create combined bundles for a platform, which can then be used by
frosty/patchfrosty jobs. This script moves the creation of combined bundles
to a separate build job to avoid duplication when building multiple configs
or formats for the same platform.

General setup:
    - Clean up the machine by killing running processes.
    - Initialize objects using packages from Elipy core.
    - Set data directory for the working data set.
    - Fetch pipeline binaries from network share.
    - Fetch two sets of head bundles for combining.
    - Combine the two sets of bundles using Avalanche.
    - If delta bundles are needed, create them from the combined bundles.
    - Deploy the combined bundles and delta bundles to network share.

Examples:
    * elipy --location criterion combined_bundles win64 --code-branch build-main-dre
      --code-changelist 436418 --data-branch build-main-dre --data-changelist 436418
      --combine-code-branch build-release --combine-code-changelist 436400
      --combine-data-branch build-release --combine-data-changelist 436400
      --data-directory Data
    * elipy --location criterion combined_bundles win64 --code-branch build-main-dre
      --code-changelist 436418 --data-branch build-main-dre --data-changelist 436418
      --combine-code-branch build-release --combine-code-changelist 436400
      --combine-data-branch build-release --combine-data-changelist 436400
      --data-directory Data --create-delta-bundles --disc-code-branch build-release
      --disc-code-changelist 371518 --disc-data-branch build-release
      --disc-data-changelist 371518
"""

import os
import shutil

import click

from elipy2 import (
    avalanche,
    filer,
    frostbite_core,
    local_paths,
    LOGGER,
    running_processes,
)
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.frostbite import icepick
from elipy2.telemetry import collect_metrics

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags


@click.command(
    "combined_bundles",
    short_help="Creates combined bundles for a platform that can be used by "
    "frosty/patchfrosty jobs.",
)
@click.argument("platform")
@click.option(
    "--data-directory",
    required=True,
    help="Which data directory to use for the working data set.",
)
@click.option(
    "--code-branch",
    required=True,
    help="Branch/stream to fetch the first set of code/binary build from.",
)
@click.option(
    "--code-changelist", required=True, help="Changelist of first set of binaries to fetch."
)
@click.option(
    "--data-branch", required=True, help="Branch/stream that first set of data is coming from."
)
@click.option(
    "--data-changelist", required=True, help="Changelist of first set of data being used."
)
@click.option(
    "--combine-code-branch",
    required=True,
    help="Branch for the second set of binaries to use in the combine workflow.",
)
@click.option(
    "--combine-code-changelist",
    required=True,
    help="Changelist for the second set of binaries to use in the combine workflow.",
)
@click.option(
    "--combine-data-branch",
    required=True,
    help="Branch for the second set of data to use in the combine workflow.",
)
@click.option(
    "--combine-data-changelist",
    required=True,
    help="Changelist for the second set of data to use in the combine workflow.",
)
@click.option(
    "--combine-settings-file",
    type=str,
    default=None,
    help="Settings file used for the combine process",
)
@click.option(
    "--create-delta-bundles",
    is_flag=True,
    help="Create delta bundles from the combined bundles. Only run if needed for the stream.",
)
@click.option(
    "--disc-code-branch",
    help="Branch/stream for disc baseline code (required if creating delta bundles).",
)
@click.option(
    "--disc-code-changelist",
    help="Changelist for disc baseline code (required if creating delta bundles).",
)
@click.option(
    "--disc-data-branch",
    help="Branch/stream for disc baseline data (required if creating delta bundles).",
)
@click.option(
    "--disc-data-changelist",
    help="Changelist for disc baseline data (required if creating delta bundles).",
)
@click.option(
    "--patch-code-branch",
    help="Branch/stream for patch baseline code (for non-first patches).",
)
@click.option(
    "--patch-code-changelist",
    help="Changelist for patch baseline code (for non-first patches).",
)
@click.option(
    "--patch-data-branch",
    help="Branch/stream for patch baseline data (for non-first patches).",
)
@click.option(
    "--patch-data-changelist",
    help="Changelist for patch baseline data (for non-first patches).",
)
@click.option(
    "--first-patch",
    is_flag=True,
    help="Flag indicating this is the first patch (use disc baseline).",
)
@click.option("--dry-run", is_flag=True, help="Build without deploying to network share.")
@click.option(
    "--fetch-pipeline",
    type=bool,
    default=True,
    help="Whether or not to fetch pipeline from filer.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(  # pylint: disable=too-many-positional-arguments
    _,
    platform,
    data_directory,  # pylint: disable=unused-argument
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    combine_code_branch,
    combine_code_changelist,
    combine_data_branch,
    combine_data_changelist,
    combine_settings_file,
    create_delta_bundles,
    disc_code_branch,
    disc_code_changelist,
    disc_data_branch,
    disc_data_changelist,
    patch_code_branch,
    patch_code_changelist,
    patch_data_branch,
    patch_data_changelist,
    first_patch,
    dry_run,
    fetch_pipeline,
):
    """
    Creates combined bundles for a platform that can be used by frosty/patchfrosty jobs.

    This script combines two sets of head bundles and optionally creates delta bundles
    from the combined result. The output is deployed to the network share for use by
    other build jobs.

    Examples:
        * elipy --location criterion combined_bundles win64 --code-branch build-main-dre
          --code-changelist 436418 --data-branch build-main-dre --data-changelist 436418
          --combine-code-branch build-release --combine-code-changelist 436400
          --combine-data-branch build-release --combine-data-changelist 436400
          --data-directory Data
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    platform = platform.lower()

    # Validate delta bundle creation requirements
    if create_delta_bundles:
        if (
            not disc_code_branch
            or not disc_code_changelist
            or not disc_data_branch
            or not disc_data_changelist
        ):
            raise ELIPYException(
                "When creating delta bundles, disc baseline parameters are required: "
                "--disc-code-branch, --disc-code-changelist, --disc-data-branch, "
                "--disc-data-changelist"
            )

        if not first_patch and (
            not patch_code_branch
            or not patch_code_changelist
            or not patch_data_branch
            or not patch_data_changelist
        ):
            raise ELIPYException(
                "When creating delta bundles for non-first patches, patch baseline "
                "parameters are required: --patch-code-branch, --patch-code-changelist, "
                "--patch-data-branch, --patch-data-changelist"
            )

    # Clean up before running the job
    running_processes.kill()
    icepick.IcepickUtils.clean_local_frosty()

    # Initialize filer utils
    _filer = filer.FilerUtils()

    # Fetch pipeline binary if needed
    if fetch_pipeline:
        _filer.fetch_code(code_branch, code_changelist, "pipeline", "release")

    # Set up bundle platform mapping
    bundles_platform = platform
    if platform == "win32":
        bundles_platform = "win64"
    elif platform == "linuxserver":
        bundles_platform = "server"

    # Set up local paths for combine bundles
    bundles_location = os.path.split(
        local_paths.get_local_bundles_path(deployed_bundles_dir_name="deployed_bundles_combine")
    )[0]

    # Fetch bundles set 1 (main)
    bundles_location_main = os.path.split(
        local_paths.get_local_bundles_path(deployed_bundles_dir_name="deployed_bundles_main")
    )[0]

    LOGGER.info(
        "Fetching first set of bundles from %s@%s, %s@%s",
        code_branch,
        code_changelist,
        data_branch,
        data_changelist,
    )
    _filer.fetch_head_bundles(
        code_branch=code_branch,
        code_changelist=code_changelist,
        data_branch=data_branch,
        data_changelist=data_changelist,
        platform=bundles_platform,
        dest=bundles_location_main,
        bundles_dir_name="combine_bundles",
    )

    # Fetch bundles set 2 (combine)
    bundles_location_combine = os.path.split(
        local_paths.get_local_bundles_path(
            deployed_bundles_dir_name="deployed_bundles_combine_second"
        )
    )[0]

    LOGGER.info(
        "Fetching second set of bundles from %s@%s, %s@%s",
        combine_code_branch,
        combine_code_changelist,
        combine_data_branch,
        combine_data_changelist,
    )
    _filer.fetch_head_bundles(
        code_branch=combine_code_branch,
        code_changelist=combine_code_changelist,
        data_branch=combine_data_branch,
        data_changelist=combine_data_changelist,
        platform=bundles_platform,
        dest=bundles_location_combine,
        bundles_dir_name="combine_bundles",
    )

    # Combine the two sets of bundles
    LOGGER.info("Combining the two sets of bundles")
    extra_combine_args = ["-s"]
    if combine_settings_file:
        extra_combine_args.append(combine_settings_file)
    else:
        if platform == "xbsx":
            extra_combine_args.append("project-combine-hres-smart-delivery.yaml")
        else:
            extra_combine_args.append("project-combine-hres.yaml")

    avalanche.combine(
        input_dir_1=bundles_location_main,
        input_dir_2=bundles_location_combine,
        output_dir=bundles_location,
        extra_combine_args=extra_combine_args,
    )

    # Deploy combined bundles to network share if not dry run
    if not dry_run:
        LOGGER.info("Deploying combined bundles to network share")
        filer.FilerUtils.deploy_avalanche_combine_output(
            source=bundles_location,
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            output_dir_name="combined_bundles",
            combine_data_branch=combine_data_branch,
            combine_data_changelist=combine_data_changelist,
            combine_code_branch=combine_code_branch,
            combine_code_changelist=combine_code_changelist,
        )

    # Create delta bundles if requested
    if create_delta_bundles:
        LOGGER.info("Creating delta bundles from combined bundles")

        # Set up paths for delta bundle creation
        local_bundles = os.path.join(frostbite_core.get_tnt_root(), "local", "combine_bundles")
        local_headbundles = os.path.join(local_bundles, "head")
        local_basebundles = os.path.join(local_bundles, "base")
        local_deltabundles = os.path.join(local_bundles, "delta")

        # Copy combined bundles to head location
        bundles_location_data = os.path.join(bundles_location, "deployed_bundles_combine")
        if os.path.exists(bundles_location_data):
            if os.path.exists(local_headbundles):
                shutil.rmtree(local_headbundles)
            shutil.copytree(bundles_location_data, local_headbundles)

        # Determine baseline for delta creation
        if first_patch:
            # For first patch, use disc baseline
            baseline_code_branch = disc_code_branch
            baseline_code_changelist = disc_code_changelist
            baseline_data_branch = disc_data_branch
            baseline_data_changelist = disc_data_changelist
            LOGGER.info(
                "Using disc baseline for first patch: %s@%s, %s@%s",
                baseline_code_branch,
                baseline_code_changelist,
                baseline_data_branch,
                baseline_data_changelist,
            )
        else:
            # For subsequent patches, use patch baseline
            baseline_code_branch = patch_code_branch
            baseline_code_changelist = patch_code_changelist
            baseline_data_branch = patch_data_branch
            baseline_data_changelist = patch_data_changelist
            LOGGER.info(
                "Using patch baseline: %s@%s, %s@%s",
                baseline_code_branch,
                baseline_code_changelist,
                baseline_data_branch,
                baseline_data_changelist,
            )

        # Fetch baseline bundles
        if first_patch:
            # For first patch, fetch head bundles from disc baseline
            _filer.fetch_baseline_bundles_head(
                data_branch=baseline_data_branch,
                data_changelist=baseline_data_changelist,
                code_branch=baseline_code_branch,
                code_changelist=baseline_code_changelist,
                platform=platform,
                dest=local_basebundles,
            )
        else:
            # For subsequent patches, fetch delta bundles from patch baseline
            _filer.fetch_baseline_bundles(
                data_branch=baseline_data_branch,
                data_changelist=baseline_data_changelist,
                code_branch=baseline_code_branch,
                code_changelist=baseline_code_changelist,
                platform=platform,
                dest=local_basebundles,
            )

        # Create delta bundles using Avalanche
        LOGGER.info("Creating delta bundles using AvalancheCLI")
        avalanche.ddelta(local_headbundles, local_basebundles, local_deltabundles)

        # Deploy delta bundles to network share if not dry run
        if not dry_run:
            LOGGER.info("Deploying delta bundles to network share")
            filer.FilerUtils.deploy_delta_bundles(
                source=local_deltabundles,
                data_branch=data_branch,
                data_changelist=data_changelist,
                code_branch=code_branch,
                code_changelist=code_changelist,
                platform=platform,
                bundles_dir_name="combine_bundles",
                combine_build_delta=True,
                combine_data_branch=combine_data_branch,
                combine_data_changelist=combine_data_changelist,
                combine_code_branch=combine_code_branch,
                combine_code_changelist=combine_code_changelist,
            )

    LOGGER.info("Combined bundles script completed successfully")


if __name__ == "__main__":
    cli()  # pylint: disable=no-value-for-parameter
